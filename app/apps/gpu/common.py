GPU_Monitor_Metrics = [{
    "GPU_Avg_Utilization": "avg(node:aicp_gpu_device:gpu_utilization{node=~'($1)', device_num=~'($2)', device_name=~'($3)'})"
}, {
    "GPU_Avg_Used_Percent": "avg(node:aicp_gpu_device:gpu_memory_utilization{node=~'($1)', device_num=~'($2)', device_name=~'($3)'})"
}, {
    "GPU_Avg_Temp": "avg(node:aicp_gpu_device:gpu_temperature{node=~'($1)', device_num=~'($2)', device_name=~'($3)'})"
}, {
    "GPU_Avg_Power": "avg(node:aicp_gpu_device:gpu_power_usage{node=~'($1)', device_num=~'($2)', device_name=~'($3)'})"
}, {
    "GPU_Utilization": "sum by (node, device_num)(node:aicp_gpu_device:gpu_utilization{node=~'($1)', device_num=~'($2)', device_name=~'($3)'})"
}, {
    "GPU_Framebuffer_Mem_Used_GB": "sum by (node, device_num)(node:aicp_gpu_device:gpu_memory_used_bytes{node=~'($1)', device_num=~'($2)', device_name=~'($3)'} / 1024 / 1024/ 1024)"
}, {
    "GPU_Temperature": "sum by (node, device_num)(node:aicp_gpu_device:gpu_temperature{node=~'($1)', device_num=~'($2)', device_name=~'($3)'})"
}, {
    "GPU_Power_Usage": "sum by (node, device_num)(node:aicp_gpu_device:gpu_power_usage{node=~'($1)', device_num=~'($2)', device_name=~'($3)'})"
}, {
    "GPU_SM_Clocks": "sum by (node, device_num)(node:aicp_gpu_device:sm_clock{node=~'($1)', device_num=~'($2)', device_name=~'($3)'})"
}, {
    "Tensor_Core_Utilization": "sum by (node, device_num)(node:aicp_gpu_device:tensor_core_utilization{node=~'($1)', device_num=~'($2)', device_name=~'($3)'})"
}]

GPU_Monitor_Metrics_Labels = [{
    "GPU_Node_Labels": "group by (node) (node:aicp_gpu_device:gpu_memory_utilization)"
}, {
    "GPU_Device_Num_Labels": "group by (device_num) (node:aicp_gpu_device:gpu_memory_utilization)"
}, {
    "GPU_Device_Name_Labels": "group by (device_name) (node:aicp_gpu_device:gpu_memory_utilization)"
}]

AICP_Overview_Node_Status = [{
    "All_Node_Role": "(sum (kube_node_role{role='control-plane'})or vector(0)) + (sum (kube_node_role{role='worker'})or vector(0))"
}, {
    "Control_Plane_Role": "	sum (kube_node_role{role='control-plane'})or vector(0)"
}, {
    "Worker_Node_Role": "sum (kube_node_role{role='worker'})or vector(0)"
}, {
    "Worker_Node_Ready_False": "sum (kube_node_status_condition{condition='Ready', status='false'})or vector(0)"
}, {
    "Worker_Node_Ready_True": "(sum (kube_node_status_condition{condition='Ready', status='true'})or vector(0)) - (sum (kube_node_status_condition{condition='Ready', status='false'})or vector(0)) - (sum(kube_node_spec_unschedulable > 0) or vector(0))"
}, {
    "All_Node_Unschedule": "sum(kube_node_spec_unschedulable > 0)or vector(0)"
}]

AICP_Overview_Manager_Node = [{
    "CPU_Avg_Utilization": "avg(node:aicp_control_plane:node_cpu_utilization:5m)"
}, {
    "Memory_Avg_Utilization": "avg(node:aicp_control_plane:node_memory_utilization)"
}, {
    "Disk_Total": "avg(node:aicp_control_plane:node_filesystem_size_bytes)"
}, {
    "Disk_Utilization": "(avg(node:aicp_control_plane:node_filesystem_size_bytes) - avg(node:aicp_control_plane:node_filesystem_avail_bytes))  / avg(node:aicp_control_plane:node_filesystem_size_bytes) * 100"
}]

AICP_Overview_GPU_Cards = [{
    "GPU_Cards": "(count(node:aicp_gpu_device_overview:gpu_memory_utilization{device_name=~'($3)'})or vector(0)) - (count(node:aicp_gpu_device_overview:gpu_memory_utilization{device_name=~'($3)', device_gpu_status='unschedulable'})or vector(0))"
}, {
    "GPU_Used": "count(node:aicp_gpu_device_overview:gpu_memory_utilization{device_gpu_status=~'^used_by_.*$', device_name=~'($3)'})or vector(0)"
}, {
    "GPU_Idle": "count(node:aicp_gpu_device_overview:gpu_memory_utilization{device_gpu_status='idle', device_name=~'($3)'})or vector(0)"
}, {
    "GPU_Unschedule": "count(node:aicp_gpu_device_overview:gpu_memory_utilization{device_gpu_status='unschedulable', device_name=~'($3)'})or vector(0)"
}, {
    "GPU_Avg_Utilization": "avg(node:aicp_gpu_device_overview:gpu_memory_utilization{device_name=~'($3)'})"
}, {
    "GPU_Avg_Used_Percent": "avg(node:aicp_gpu_device_overview:gpu_utilization{device_name=~'($3)'})"
}, {
    "Used_GPU_Memory_Utilization": "avg(node:aicp_gpu_device_overview:gpu_memory_utilization{device_gpu_status=~'^used_by_.*$', device_name=~'($3)'})"
}, {
    "Used_GPU_Utilization": "avg(node:aicp_gpu_device_overview:gpu_utilization{device_gpu_status=~'^used_by_.*$', device_name=~'($3)'})"
}]

AICP_Overview_Work_Nodes = [{
    "Worker_Node": "(count(node:aicp_work_node:node_memory_utilization{device_name=~'($3)'})or vector(0)) - (count(node:aicp_work_node:node_memory_utilization{node_gpu_status=~'unschedulable_.*$', device_name=~'($3)'})or vector(0))"
}, {
    "Worker_Used": "count(node:aicp_work_node:node_memory_utilization{node_gpu_status=~'^used_by_.*$', device_name=~'($3)'})or vector(0)"
}, {
    "Worker_Idle": "count(node:aicp_work_node:node_memory_utilization{node_gpu_status=~'idle_.*$', device_name=~'($3)'})or vector(0)"
}, {
    "Worker_Unschedule": "count(node:aicp_work_node:node_memory_utilization{node_gpu_status=~'unschedulable_.*$', device_name=~'($3)'})or vector(0)"
}, {
    "CPU_Avg_Utilization": "avg(node:aicp_work_node:node_memory_utilization{device_name=~'($3)'})"
}, {
    "Memory_Avg_Utilization": "avg(node:aicp_work_node:node_cpu_utilization:5m{device_name=~'($3)'})"
}, {
    "Disk_Total": "sum(node:aicp_work_node:node_filesystem_size_bytes{device_name=~'($3)'})"
}, {
    "Disk_Available": "sum(node:aicp_work_node:node_filesystem_avail_bytes{device_name=~'($3)'})"
}, {
    "Disk_Remaining": "sum(node:aicp_work_node:node_filesystem_size_bytes{device_name=~'($3)'}) - sum(node:aicp_work_node:node_filesystem_avail_bytes{device_name=~'($3)'})"
}]

Worker_Monitor_Metrics = [{
    "Worker_Node": "(count(node:aicp_work_node:node_memory_utilization{node=~'($1)', device_name=~'($3)'})or vector(0)) - (count(node:aicp_work_node:node_memory_utilization{node=~'($1)', node_gpu_status=~'unschedulable_.*$', device_name=~'($3)'})or vector(0))"
}, {
    "Worker_Used": "count(node:aicp_work_node:node_memory_utilization{node=~'($1)', node_gpu_status=~'^used_by_.*$', device_name=~'($3)'})or vector(0)"
}, {
    "Worker_Idle": "count(node:aicp_work_node:node_memory_utilization{node=~'($1)', node_gpu_status=~'idle_.*$', device_name=~'($3)'})or vector(0)"
}, {
    "Worker_Unschedule": "count(node:aicp_work_node:node_memory_utilization{node=~'($1)', node_gpu_status=~'unschedulable_.*$', device_name=~'($3)'})or vector(0)"
}, {
    "CPU_Avg_Utilization": "avg(node:aicp_work_node:node_memory_utilization{node=~'($1)', device_name=~'($3)'})"
}, {
    "Memory_Avg_Utilization": "avg(node:aicp_work_node:node_cpu_utilization:5m{node=~'($1)', device_name=~'($3)'})"
}, {
    "GPU_Cards": "(count(node:aicp_gpu_device_overview:gpu_memory_utilization{node=~'($1)', device_name=~'($3)'})or vector(0)) - (count(node:aicp_gpu_device_overview:gpu_memory_utilization{node=~'($1)', device_name=~'($3)', device_gpu_status='unschedulable'})or vector(0))"
}, {
    "GPU_Used": "count(node:aicp_gpu_device_overview:gpu_memory_utilization{node=~'($1)', device_gpu_status=~'^used_by_.*$', device_name=~'($3)'})or vector(0)"
}, {
    "GPU_Idle": "count(node:aicp_gpu_device_overview:gpu_memory_utilization{node=~'($1)', device_gpu_status='idle', device_name=~'($3)'})or vector(0)"
}, {
    "GPU_Unschedule": "count(node:aicp_gpu_device_overview:gpu_memory_utilization{node=~'($1)', device_gpu_status='unschedulable', device_name=~'($3)'})or vector(0)"
}, {
    "GPU_Avg_Utilization": "avg(node:aicp_gpu_device_overview:gpu_utilization{node=~'($1)', device_name=~'($3)'})"
}, {
    "GPU_Avg_Used_Percent": "avg(node:aicp_gpu_device_overview:gpu_memory_utilization{node=~'($1)', device_name=~'($3)'})"
}, {
    "Used_GPU_Memory_Utilization": "avg(node:aicp_gpu_device_overview:gpu_memory_utilization{node=~'($1)', device_gpu_status=~'^used_by_.*$', device_name=~'($3)'})"
}, {
    "Used_GPU_Utilization": "avg(node:aicp_gpu_device_overview:gpu_utilization{node=~'($1)', device_gpu_status=~'^used_by_.*$', device_name=~'($3)'})"
}, {
    "Disk_Total": "sum(node:aicp_work_node:node_filesystem_size_bytes{node=~'($1)', device_name=~'($3)'})"
}, {
    "Disk_Available": "sum(node:aicp_work_node:node_filesystem_avail_bytes{node=~'($1)', device_name=~'($3)'})"
}, {
    "Disk_Remaining": "sum(node:aicp_work_node:node_filesystem_size_bytes{node=~'($1)', device_name=~'($3)'}) - sum(node:aicp_work_node:node_filesystem_avail_bytes{node=~'($1)', device_name=~'($3)'})"
}]

Control_Plane_Metrics = [{
    "CPU_Avg_Utilization": "avg(node:aicp_control_plane:node_cpu_utilization:5m)"
}, {
    "Memory_Avg_Utilization": "avg(node:aicp_control_plane:node_memory_utilization)"
}, {
    "Disk_Remaining": "sort_desc((node:aicp_work_node:node_filesystem_size_bytes - on (node, device, device_name)node:aicp_work_node:node_filesystem_avail_bytes)* on (node) group_left ()kube_node_role{role='control-plane'})"
}, {
    "Disk_Available": "sort_desc(node:aicp_work_node:node_filesystem_avail_bytes* on (node) group_left ()kube_node_role{role='control-plane'})"
}, {
    "Disk_Total": "sort_desc(node:aicp_work_node:node_filesystem_size_bytes* on (node) group_left ()kube_node_role{role='control-plane'})"
}, {
    "Disk_Utilization": "((node:aicp_work_node:node_filesystem_size_bytes - on (node, device, device_name) node:aicp_work_node:node_filesystem_avail_bytes)* on (node) group_left ()kube_node_role{role='control-plane'}) / on (node, device, device_name) node:aicp_work_node:node_filesystem_size_bytes * 100"
}, {
    "Disk_Total_Remaining": "avg(sum by (node) ((node:aicp_work_node:node_filesystem_size_bytes* on (node) group_left ()kube_node_role{role='control-plane'})- on (node, device, device_name)node:aicp_work_node:node_filesystem_avail_bytes* on (node) group_left ()kube_node_role{role='control-plane'}))"
}]

Template_Handlers = {
    "GPU_Monitor_Overview": lambda: [],
    "GPU_Monitor_Metrics": lambda: GPU_Monitor_Metrics,
    "Worker_Monitor_Metrics": lambda: Worker_Monitor_Metrics,
    "Control_Plane_Metrics": lambda: Control_Plane_Metrics
}