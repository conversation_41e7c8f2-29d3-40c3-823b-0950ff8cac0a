import json
import sys
from datetime import datetime
from typing import Dict, List

import kr8s
from fastapi import APIRouter, BackgroundTasks, Depends, Query, Request, status as http_status
from kr8s import NotFoundError, ExecError
from kr8s.objects import Pod, Service
from kr8s.asyncio.objects import Pod as AsyncPod
from kubernetes.client import V1Node

import app
from app import logger
from app.apps.notebooks.background_task import create_notebooks_in_back_ground, start_notebooks_in_background, \
    stop_notebooks_in_background
from app.core.constant import NOTEBOOK_PRE_IMAGE_INFO_KEY
from app.core.qingcloud.docker_api import QingcloudDockerApiClient
from app.core.global_server.exceptions import UserHasNotPermissionException
from app.core.kube.api.kfam import KfamClient
from app.core.kube.models.kfam import WorkGroupInfo, Namespace
from app.core.qingcloud.resource import ProductCenterResource
from app.cruds.notebooks import NotebookCRUD, add_ip, add_ssh_info, PROBE_PORT_MAP
from app.apps.notebooks.dependencies import get_notebooks_crud, has_notebook_resource_permission_namespace
from app.apps.notebooks.exceptions import CannotChangeResourceGroupWithLocalDiskException, DockerRepoNotExistsException, \
    DockerRepoNotMatchException, NotebookQuotaException, \
    ResourceGroupNotebookNotSupportChangeException, \
    SkuNotEnoughException, SkuNotExistsException, VolumeExistsException, \
    VolumeNotExistsException, ResourceGroupNodeNotFoundException, NotebookNotHaveGPUException
from app.apps.notebooks.exceptions import NotebookNotRunningException, NotebookNotStoppedException
from app.apps.notebooks.kr8s_objects.notebook import NoteBook as K8SNoteBook, NoteBookCrOperator, \
    generate_notebook_init_password, generate_notebook_init_password_v2, generate_notebook_init_password_v3
from app.depends.authz import PermissionLevel
from app.depends.nb_group import notebook_has_permission, notebook_namespace_permission
from app.models.notebooks import NoteBookChangeImageParam, NoteBookMountsParam, NoteBookPortParam, \
    NoteBookStartParam, NoteBookStopParam, NoteBookPreCheckParam, \
    NoteBookChangeResourceGroupParam, \
    NoteBookUuid, NoteBookUuidDeleteParam, \
    NoteBookUuidSetStopTimeParam, Notebook, \
    NotebookCreate, NotebookRead, \
    NotebookSaveToImage, \
    NotebookSearchQueryParams, \
    NotebookServices, NotebookStartPrecheckResult, NotebookStatus, NotebookUpdateName, NotebookUpdateSource, NotebookVolumeSpec
from app.core.models import AicpServers
from app.apps.notebooks.utils import build_image_done
from app.cruds.operation_record import OperationRecordCrud
from app.cruds.public_key import PublicKeysCrud
from app.apps.public_key.dependencies import get_key_crud
from app.models.operation_record import OperationRecord
from app.models.public_key import PublicKey
from app.cruds.resource_group import ResourceGroupCrud
from app.models.resource_group import ResourceNodeStatus, ResourceTemplate
from app.apps.resource_group.utils import check_sku_salable, get_node_available_resources, get_vgpu_available_resources
from app.cruds.user import UserCRUD
from app.apps.user.dependencies import get_user_crud
from app.common.response import INSTANCE_NOT_FOUND, RESOURCE_NOT_FOUND
from app.depends import has_permission
from app.core.kube.api import create_custom_rsrc, get_allowable_nodes, get_nodes_by_names, get_resource_group_nodes
from app.core.prometheus.client import PromethusClient, PromethusAsyncClient
from app.core.qingcloud.interface import patch_user_info_for_models, product_center_query_request
from app.core.response import BaseGenericResponse, GenericMultipleResponse, GenericSingleResponse
from app.core.volumes.manager import VolumesManager

router = APIRouter(prefix="/notebooks", tags=["notebooks"])


@router.get("/users", response_model=GenericSingleResponse[WorkGroupInfo],
            summary="获取可访问的用户列表")
def get_users(
        request: Request,
        user_db: UserCRUD = Depends(get_user_crud)
):
    """
    获取各个账号可以看到的用户列表
    """
    # 获取主账号的所有绑定信息
    kfam_client: KfamClient = KfamClient(request.user.root_user_id)
    workgroup: WorkGroupInfo = kfam_client.get_all_profile(user_db)
    # 主账号返回所有的绑定信息
    if not request.user.is_subuser():
        return GenericSingleResponse[WorkGroupInfo](data=workgroup)
    # 子账号根据权限返回
    permission = next(item["permission"] for item in request.user.permissions if item["module"] == "NB")
    if permission == "NO":
        raise UserHasNotPermissionException()
    if permission == "OWN":
        for item in workgroup.namespaces:
            if item.namespace_user_id == request.user.user_id:
                ns_data = Namespace(
                    user=item.user,
                    namespace=item.namespace,
                    namespace_user_id=item.namespace_user_id,
                    role=item.role,
                    email=item.email
                )
                res_data = WorkGroupInfo(
                    user=request.user.user_id,
                    namespaces=[ns_data],
                    isClusterAdmin=workgroup.is_cluster_admin
                )
                return GenericSingleResponse[WorkGroupInfo](data=res_data)
    if permission in ["READALL", "ALL"]:
        return GenericSingleResponse[WorkGroupInfo](data=workgroup)


@router.get("/sources", response_model=GenericMultipleResponse[Dict],
            summary="获取可用的源",
            dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.READ))])
def get_sources():
    """
    获取可用的源
    :param request:
    :return:
    """
    # 开了代理，走resource-proxy，不能进行配置
    # if app.settings.NOTEBOOK_ENABLE_PROXY:
    #    return GenericMultipleResponse[Dict](data=[], counts=0)
    data = [
        {
            "source_key": 'pip',
            "source_values": ["pypi", "aliyun", "tsinghua", "huaweicloud"]
        },
        {
            "source_key": 'conda',
            "source_values": ["conda", "aliyun", "tsinghua"]
        },
        {
            "source_key": 'apt',
            "source_values": ["ubuntu", "aliyun", "tsinghua", "huaweicloud"]
        }
    ]
    return GenericMultipleResponse[Dict](data=data, counts=len(data))


@router.post("/namespaces/{namespace}/sources", response_model=BaseGenericResponse,
             summary="更换安装源",
             dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.WRITE))])
async def update_notebook_sources(
        req: NotebookUpdateSource,
        request: Request,
        namespace: str = Depends(notebook_namespace_permission(permission_level=PermissionLevel.WRITE)),
        notebook_session: NotebookCRUD = Depends(get_notebooks_crud),
):
    """
    notebook换源
    """
    user_id = request.user.user_id
    uuid = req.uuid
    logger.info(f"{user_id}的{uuid}容器实例，{req.source_key}要换成{req.source_val}的源.")
    await notebook_session.update_notebook_sources(uuid, req.source_key, req.source_val)
    return BaseGenericResponse()


@router.get("/namespaces/{namespace}/notebooks", response_model=GenericMultipleResponse[NotebookRead],
            summary="获取容器实例列表",
            dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.READ))])
def get_notebooks(
        request: Request,
        namespace: str = Depends(notebook_namespace_permission(permission_level=PermissionLevel.READ)),
        notebooks_session: NotebookCRUD = Depends(get_notebooks_crud),
        query: NotebookSearchQueryParams = Depends(NotebookSearchQueryParams),
):
    """
    获取容器实例列表
    """
    count, notebooks = notebooks_session.search(namespace, query)
    notebooks_session.patch_os_disk(notebooks)
    notebookreads = [NotebookRead.from_orm(notebook) for notebook in notebooks]
    notebooks_session.patch_delete_time(notebookreads)
    patch_user_info_for_models(notebookreads)
    add_ip(notebookreads)
    if namespace != "ALL":
        add_ssh_info(namespace, notebookreads)

    return GenericMultipleResponse[NotebookRead](data=notebookreads, counts=count)


@router.post("/namespaces/{namespace}/create", response_model=GenericMultipleResponse[NotebookRead],
             summary="创建容器实例",
             dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.WRITE))])
def create_notebook(
        data: NotebookCreate,
        request: Request,
        background_task: BackgroundTasks,
        namespace: str = Depends(notebook_namespace_permission(permission_level=PermissionLevel.WRITE)),
        notebooks_session: NotebookCRUD = Depends(get_notebooks_crud),
        public_key_session: PublicKeysCrud = Depends(get_key_crud),
        user_db: UserCRUD = Depends(get_user_crud)
):
    """
    创建容器实例
    """
    # Quota verification
    user_db.quota_verification(request.user.user_id, "nb", data.replica_specs.replicas, data.replica_specs.specs)

    data.namespace = namespace
    data.user_id = request.user.user_id
    notebooks_session.check_balance(data)

    if data.stop_time and not QingcloudDockerApiClient(request.user).get_repo_name():
        raise DockerRepoNotExistsException()

    notebooks = notebooks_session.create(data)
    user_keys = [PublicKey.from_orm(x) for x in public_key_session.get_pk_by_user(user_id=request.user.user_id)]
    for notebook in notebooks:
        background_task.add_task(create_notebooks_in_back_ground, notebook.uuid, data, request.user, user_keys)

    return GenericMultipleResponse[NotebookRead](data=notebooks)


@router.post("/namespaces/{namespace}/save", response_model=GenericSingleResponse[Dict],
             summary="保存容器实例镜像",
             dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.WRITE)),
                           Depends(has_notebook_resource_permission_namespace("nb_id"))])
def save_notebook(
        background_tasks: BackgroundTasks,
        req: NotebookSaveToImage,
        request: Request,
        namespace: str = Depends(notebook_namespace_permission(permission_level=PermissionLevel.WRITE)),
        notebooks_session: NotebookCRUD = Depends(get_notebooks_crud),

):
    """
    容器实例保存为镜像
    """
    rep = GenericSingleResponse[Dict](data={})
    op_session = OperationRecordCrud(session=notebooks_session.session)
    notebook = Notebook.one_by_id(req.nb_id)
    notebook.operation_user_id = request.user.user_id
    if notebook.status != NotebookStatus.Running:
        raise NotebookNotRunningException()
    background_tasks.add_task(build_image_done, req, notebook, op_session, request.user.user_id)
    return rep


@router.delete("/namespaces/{namespace}/notebooks", response_model=BaseGenericResponse,
               summary="删除容器实例",
               dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.WRITE)),
                             Depends(has_notebook_resource_permission_namespace("uuid"))])
def delete_notebooks(
        uuid: str,
        request: Request,
        background_task: BackgroundTasks,
        namespace: str = Depends(notebook_namespace_permission(permission_level=PermissionLevel.WRITE)),
        notebooks_session: NotebookCRUD = Depends(get_notebooks_crud)
):
    """
    删除容器实例
    """
    if isinstance(uuid, str):
        nb_uuids = [uuid]
    else:
        nb_uuids = uuid

    for nb_uuid in nb_uuids:
        logger.info(f"delete notebook {nb_uuid} by user {request.user.user_id}")
        notebooks_session.terminated(nb_uuid, "delete by user")
    return BaseGenericResponse()


@router.post("/namespaces/{namespace}/update", response_model=BaseGenericResponse,
             summary="修改容器实例名称",
             dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.WRITE)),
                           Depends(has_notebook_resource_permission_namespace("uuid"))])
def update_notebooks(
        req: NotebookUpdateName,
        request: Request,
        namespace: str = Depends(notebook_namespace_permission(permission_level=PermissionLevel.WRITE)),
        notebook_session: NotebookCRUD = Depends(get_notebooks_crud),
):
    """
    修改容器实例名称
    """
    user_id = request.user.user_id
    notebook_name = req.notebook_name
    uuid = req.uuid
    logger.info("当前修改名称%s", notebook_name)
    notebook_session.update_notebook_name(user_id=user_id,
                                          uuid=uuid,
                                          notebook_name=notebook_name)
    return BaseGenericResponse()


@router.get(
    "/namespaces/{namespace}/notebooks/{name}",
    response_model=GenericSingleResponse[Dict],
    status_code=http_status.HTTP_200_OK,
    summary="获取单个容器实例的k8s对象",
    dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.READ))]
)
def get_notebook(name: str,
                 namespace: str = Depends(notebook_namespace_permission(permission_level=PermissionLevel.READ))):
    """
    获取单个容器实例的K8s对象
    """
    r = K8SNoteBook.get(name, namespace=namespace)
    return GenericSingleResponse[Dict](data=r.raw)


@router.get(
    "/namespaces/{namespace}/notebooks/{name}/pods",
    response_model=GenericMultipleResponse[Dict],
    status_code=http_status.HTTP_200_OK,
    deprecated=True,
    summary="获取容器实例的pod",
    dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.READ))]
)
def get_notebooks_pods(name: str,
                       namespace: str = Depends(notebook_namespace_permission(permission_level=PermissionLevel.READ))):
    """
    获取容器实例Pod
    """
    r = kr8s.get("pods", namespace=namespace,
                 label_selector={"training.kubeflow.org/job-name": name})
    pods_raw = [i.raw for i in r]
    return GenericMultipleResponse[Dict](data=pods_raw, counts=len(pods_raw))


@router.get(
    "/namespaces/{namespace}/notebooks/{uuid}/servers",
    response_model=GenericMultipleResponse[AicpServers],
    status_code=http_status.HTTP_200_OK,
    summary="获取容器实例服务列表",
    dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.READ)),
                  Depends(has_notebook_resource_permission_namespace("uuid"))]
)
def get_notebooks_servers(
        uuid: str,
        services: List[str] = Query(..., description="jupyter, vscode, ssh, custom, node_port"),
        namespace: str = Depends(notebook_namespace_permission(permission_level=PermissionLevel.READ)),
        notebooks_session: NotebookCRUD = Depends(get_notebooks_crud)
):
    """
    获取容器实例服务列表
    """
    target_port = {80: 8888, 81: 8889, 22: 22}

    notebook = notebooks_session.get_by_uuid(uuid)
    namespace = notebook.namespace

    probe_success = PromethusClient().get_namespace_notebook_probe_success(namespace, uuid)
    ports = []
    if set(services) & {"ssh", "custom"}:
        ssh_service_id = f"{uuid}-ssh"
        ssh_service = NotebookServices.one_by_id(ssh_service_id)
        if ssh_service:
            ports = json.loads(ssh_service.server)
        else:
            node_service = Service.get(name=f"{uuid}-ssh", namespace=namespace).raw
            ports = node_service['spec']['ports']
            NotebookServices(uuid=ssh_service_id, server=json.dumps(ports)).save()

    node_service_ports = []
    if set(services) & {"node_port"}:
        node_service_id = f"{uuid}-node-port"
        node_service = NotebookServices.one_by_id(node_service_id)
        if node_service:
            node_service_ports = json.loads(node_service.server)
        else:
            try:
                node_service = Service.get(name=f"{uuid}-node-port", namespace=namespace).raw
                node_service_ports = node_service['spec']['ports']
            except NotFoundError as e:
                logger.warning(f"node port service not found: {e}")
                node_service_ports = []
            NotebookServices(uuid=node_service_id, server=json.dumps(node_service_ports)).save()


    servers = []
    for service in services:
        if service in ["jupyter", "vscode", "coder"]:
            service = "coder" if service == "vscode" else service
            servers.append(
                AicpServers(
                    server_name=service, target_port=target_port[PROBE_PORT_MAP[service]],
                    server_type=service, status=bool(probe_success.get(str(PROBE_PORT_MAP[service]), False)),
                    url=app.settings.NOTEBOOK_HOST.format(server_type=service, namespace=namespace, name=uuid)
                )
            )
        elif service in "ssh":
            try:
                ssh_version_id = f"{uuid}-ssh-version"
                ssh_version = NotebookServices.one_by_id(ssh_version_id)
                if ssh_version:
                    password_version = ssh_version.server
                else:
                    notebook: K8SNoteBook = K8SNoteBook.get(uuid, namespace=namespace)  # noqa
                    password_version = notebook.annotations.get("notebooks.kubeflow.org/password-version", "v1")
                    NotebookServices(uuid=ssh_version_id, server=password_version).save()
                if password_version == "v2":
                    password = generate_notebook_init_password_v2(uuid)
                elif password_version == "v3":
                    password = generate_notebook_init_password_v3(uuid)
                else:
                    password = generate_notebook_init_password(uuid)
                for port in ports:
                    if port['name'].split('-')[0] == "ssh":
                        servers.append(
                            AicpServers(
                                server_name=service, target_port=PROBE_PORT_MAP[service],
                                server_type=service, status=bool(probe_success.get(str(PROBE_PORT_MAP[service]), False)),
                                url=f"ssh root@{app.settings.SSH_HOST} -p {port['nodePort']}",
                                password=password
                            )
                        )
            except NotFoundError as e:
                logger.warning(f"ssh service not found: {e}")
                raise NotebookNotRunningException()
        elif service == "custom":
            for port in ports:
                if port['name'].split('-')[0] == "custom":
                    server_type = server_name = port['name'].split('-')[0]
                    servers.append(
                        AicpServers(
                            server_name=server_type, target_port=port["targetPort"],
                            server_type=server_name, status=False,
                            protocol="http" if port.get('appProtocol') is None else port.get('appProtocol'),
                            url=f"{app.settings.SSH_HOST}:{port['nodePort']}"
                        )
                    )
        elif service == "node_port":
            for port in node_service_ports:
                server_type = server_name = service
                servers.append(
                    AicpServers(
                        server_name=server_type, target_port=port["targetPort"],
                        server_type=server_name, status=False,
                        protocol="http" if port.get('appProtocol') is None else port.get('appProtocol'),
                        url=f"{app.settings.SSH_HOST}:{port['nodePort']}"
                    )
                )
    return GenericMultipleResponse[AicpServers](data=servers, counts=len(servers))


@router.get(
    "/namespaces/{namespace}/notebooks/{name}/pod/{pod_name}/log",
    response_model=GenericMultipleResponse[str],
    status_code=http_status.HTTP_200_OK,
    deprecated=True,
    summary="获取容器实例的运行log",
    dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.READ))]
)
def get_notebooks_pod_log(name: str, pod_name: str, namespace: str = Depends(
    notebook_namespace_permission(permission_level=PermissionLevel.READ)), ):
    """
    获取容器实例的运行log
    """
    pod = Pod.get(name=pod_name, namespace=namespace)
    logs = []
    for line in pod.logs(container="tensorflow"):
        logs.append(line)
    return GenericSingleResponse[List[str]](data=logs)


@router.get(
    "/namespaces/{namespace}/notebooks/{name}/pod/{pod_name}/events",
    response_model=GenericMultipleResponse[Dict],
    status_code=http_status.HTTP_200_OK,
    deprecated=True,
    summary="获取容器实例的events",
    dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.READ))]
)
def get_notebooks_pod_events(name: str, pod_name: str,
                             namespace: str = Depends(
                                 notebook_namespace_permission(permission_level=PermissionLevel.READ))):
    """
    获取容器实例的events
    """
    field_selector = {"involvedObject.name": pod_name, "involvedObject.kind": "Pod"}
    r = kr8s.get("events", namespace=namespace, field_selector=field_selector)
    return GenericMultipleResponse[Dict](data=[i.raw for i in r], counts=len(r))


@router.post(
    "/namespaces/{namespace}/notebooks/set_stop_time",
    response_model=GenericSingleResponse[Notebook],
    status_code=http_status.HTTP_200_OK,
    summary="设置容器实例自动暂停时间",
    dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.WRITE)),
                  Depends(has_notebook_resource_permission_namespace("uuid"))]
)
def set_notebooks_stop_time(
        request: Request,
        body: NoteBookUuidSetStopTimeParam,
        background_task: BackgroundTasks,
        namespace: str = Depends(notebook_namespace_permission(permission_level=PermissionLevel.WRITE)),
        notebooks_session: NotebookCRUD = Depends(get_notebooks_crud)
):
    """
    设置容器实例自动关机时间
    """
    notebook = Notebook.one_by_id(body.uuid, session_=notebooks_session.session)
    if notebook.user_id != request.user.user_id:
        user = notebook.get_user()
    else:
        user = request.user
    if not QingcloudDockerApiClient(user).get_repo_name():
        raise DockerRepoNotExistsException()
    notebook.stop_time = body.stop_time
    return GenericSingleResponse[Notebook](data=notebook)


@router.post(
    "/namespaces/{namespace}/notebooks/stop",
    response_model=GenericMultipleResponse[Notebook],
    status_code=http_status.HTTP_200_OK,
    dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.WRITE)),
                  Depends(has_notebook_resource_permission_namespace("uuids"))]
)
def stop_notebooks(
        request: Request,
        body: NoteBookStopParam,
        background_task: BackgroundTasks,
        namespace: str = Depends(notebook_namespace_permission(permission_level=PermissionLevel.WRITE)),
        notebooks_session: NotebookCRUD = Depends(get_notebooks_crud)
):
    """
    暂停容器实例
    """
    notebooks = Notebook.all_by_ids(body.uuids, session_=notebooks_session.session)
    if not notebooks_session.check_all_running(notebooks):
        raise NotebookNotRunningException()

    for notebook in notebooks:
        # if body.save_image:
        #     notebook_cr = NoteBookCrOperator(notebook.uuid, notebook.namespace, notebook.user_id)
        #     notebook_cr.precheck_for_save_image()

        background_task.add_task(stop_notebooks_in_background, notebook.uuid, save_image=body.save_image)

    return GenericMultipleResponse[Notebook](data=notebooks)


@router.post(
    "/namespaces/{namespace}/notebooks/start",
    response_model=GenericMultipleResponse[Notebook],
    status_code=http_status.HTTP_200_OK,
    summary="启动容器实例",
    dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.WRITE)),
                  Depends(has_notebook_resource_permission_namespace("uuids"))]
)
def start_notebooks(
        request: Request,
        body: NoteBookStartParam,
        background_task: BackgroundTasks,
        namespace: str = Depends(notebook_namespace_permission(permission_level=PermissionLevel.WRITE)),
        notebooks_session: NotebookCRUD = Depends(get_notebooks_crud)
):
    """
    启动容器实例
    """
    # notebooks only support restart one
    user_db = UserCRUD(session=notebooks_session.session, user=request.user)
    notebooks: List[Notebook] = Notebook.all_by_ids(body.uuids, session_=notebooks_session.session)
    if not notebooks_session.check_all_suspended(notebooks):
        raise NotebookNotStoppedException()

    new_sku_ids = [body.sku_id] if body.sku_id else [x.replica_specs.specs for x in notebooks if
                                                     not x.is_resource_group()]

    if new_sku_ids:
        logger.info(f"new_sku_ids : {new_sku_ids}")
        # 更换规格是否需要校验额度
        if body.sku_id:
            user_db.check_start_quota(body.uuids, body.sku_id, request.user.user_id)
            for notebook in notebooks:
                notebook_cro = NoteBookCrOperator(notebook.uuid, notebook.namespace, notebook.user_id)
                notebook_cro.check_image_creating()
        # 检查sku是否已下架
        skus = product_center_query_request(values=["container_instance"], limit=500)
        sku_map = {x["sku_id"]: x for x in skus["skus"]}
        if set(new_sku_ids) - set(sku_map.keys()):
            raise SkuNotExistsException()

        allowable_nodes: List[V1Node] = get_allowable_nodes()
        nodes_status: List[ResourceNodeStatus] = get_node_available_resources(allowable_nodes)
        nodes_vgpu_status = get_vgpu_available_resources(allowable_nodes)

        for sku_id in new_sku_ids:
            if not check_sku_salable(sku_map[sku_id], allowable_nodes, nodes_status, nodes_vgpu_status)[0]:
                raise SkuNotEnoughException()
    elif any([x.is_resource_group() for x in notebooks]):

        # Switching the resource group when there is zfs will cause the notebook to fail to start.
        if body.sku_id is not None:
            raise ResourceGroupNotebookNotSupportChangeException()
        for notebook in notebooks:
            logger.debug(f"notebook : {notebook.replica_specs}")
            rgc = ResourceGroupCrud(session=notebooks_session.session)
            rgc.get_resource_group_node_by_user_id(
                notebook.replica_specs.rg_id, notebook.replica_specs.specs, notebooks_session.user.user_id,
                gpu_name=notebook.replica_specs.custom_gpu_name)
            if body.custom_spec:

                if body.custom_spec.template_id:
                    template = ResourceTemplate.one_by_id(body.custom_spec.template_id,
                                                          session_=notebooks_session.session)
                    body.custom_spec.sqlmodel_update(template.to_spec())
                logger.debug(f"custom_spec : {body.custom_spec}")
                # Switching the resource group when there is zfs will cause the notebook to fail to start.
                if notebook.get_zfs_volume() and body.custom_spec.rg_id != notebook.replica_specs.rg_id:
                    raise CannotChangeResourceGroupWithLocalDiskException()

                rg_sku_info = body.custom_spec.rg_custom_to_sku_dict()
                logger.debug(f"custom_spec : {body.custom_spec}")
                resource_node = ResourceGroupCrud(session=notebooks_session.session).get_rg_node_with_user_permission(
                    user_id=notebook.user_id, rg_id=body.custom_spec.rg_id, rg_node_id=body.custom_spec.specs)
                if resource_node is None:
                    raise ResourceGroupNodeNotFoundException()
                logger.debug(f"resource_node : {resource_node}")
                body.custom_spec.custom_gpu_type = resource_node.gpu_model
                body.custom_spec.custom_gpu_name = resource_node.gpu_name
            else:
                logger.debug(f"notebook : {notebook.replica_specs}")
                rg_sku_info = notebook.replica_specs.rg_custom_to_sku_dict()
            allowable_nodes: List[V1Node] = get_resource_group_nodes(notebook.replica_specs.rg_id)
            nodes_status: List[ResourceNodeStatus] = get_node_available_resources(allowable_nodes)
            nodes_vgpu_status = get_vgpu_available_resources(allowable_nodes)
            if not check_sku_salable(rg_sku_info, allowable_nodes, nodes_status, nodes_vgpu_status)[0]:
                raise SkuNotEnoughException()

    for notebook in notebooks:
        notebook.check_resource_balance()
        notebook.operation_user_id = request.user.user_id
        logger.debug(f"notebook : {body}")
        background_task.add_task(start_notebooks_in_background, notebook.uuid, body, request.user)

    return GenericMultipleResponse[Notebook](data=notebooks)


@router.post(
    "/namespaces/{namespace}/notebooks/start/pre_check",
    response_model=GenericSingleResponse[NotebookStartPrecheckResult],
    status_code=http_status.HTTP_200_OK,
    summary="启动容器实例前置检查, 是否可以在原节点启动",
    dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.WRITE)),
                  Depends(has_notebook_resource_permission_namespace("uuid"))]
)
def start_notebooks_pre_check(
        request: Request,
        body: NoteBookPreCheckParam,
        namespace: str = Depends(notebook_namespace_permission(permission_level=PermissionLevel.WRITE)),
        notebooks_session: NotebookCRUD = Depends(get_notebooks_crud)
):
    notebook = Notebook.one_by_id(body.uuid, session_=notebooks_session.session)
    notebook_cro = NoteBookCrOperator(notebook.uuid, notebook.namespace, notebook.user_id)
    allowable_nodes: List[V1Node] = get_allowable_nodes()
    nodes_status: List[ResourceNodeStatus] = get_node_available_resources(allowable_nodes)
    nodes_vgpu_status = get_vgpu_available_resources(allowable_nodes)
    if notebook.replica_specs.specs and notebook.replica_specs.specs.startswith("sku"):
        sku_id = body.sku_id or notebook.replica_specs.specs
        sku_info = product_center_query_request(search_word=sku_id)["skus"][0]
        salable, max_applicable_number, salable_detail = check_sku_salable(sku_info, allowable_nodes, nodes_status, nodes_vgpu_status)
        logger.debug(f"pre_check_notebook with sku[{sku_id}] : {salable} | {max_applicable_number} | {salable_detail}")
    else:
        result = NotebookStartPrecheckResult(can_start=True, can_start_on_pre_node=True)
        return GenericSingleResponse[NotebookStartPrecheckResult](data=result)
    if not salable:
        result = NotebookStartPrecheckResult(can_start=False, can_start_on_pre_node=False)
    else:
        if NOTEBOOK_PRE_IMAGE_INFO_KEY not in notebook_cro.notebook_cr["metadata"]["annotations"]:
            result = NotebookStartPrecheckResult(can_start=True, can_start_on_pre_node=True)
        else:
            pre_node, _ = notebook_cro.notebook_cr.raw["metadata"]["annotations"][NOTEBOOK_PRE_IMAGE_INFO_KEY].split("|")
            pre_node_salable = [ x for x in salable_detail if x["hostname"] == pre_node]
            if pre_node_salable and pre_node_salable[0]["applicable_number"]:
                result = NotebookStartPrecheckResult(can_start=True, can_start_on_pre_node=True)
            else:
                result = NotebookStartPrecheckResult(can_start=True, can_start_on_pre_node=False)

    return GenericSingleResponse[NotebookStartPrecheckResult](data=result)

@router.post(
    "/namespaces/{namespace}/notebooks/set_delete_time",
    response_model=GenericSingleResponse[Notebook],
    status_code=http_status.HTTP_200_OK,
    summary="设置容器实例自动释放时间",
    dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.WRITE)),
                  Depends(has_notebook_resource_permission_namespace("uuid"))]
)
def set_notebooks_delete_time(
        request: Request,
        body: NoteBookUuidDeleteParam,
        background_task: BackgroundTasks,
        namespace: str = Depends(notebook_namespace_permission(permission_level=PermissionLevel.WRITE)),
        notebooks_session: NotebookCRUD = Depends(get_notebooks_crud)
):
    """
    设置容器实例自动释放时间
    """
    notebook = Notebook.one_by_id(body.uuid, session_=notebooks_session.session)

    OperationRecord.create_by_resource(notebook, action="SetDeleteTime", status='SetDeleteTime',
                                       reason='SetDeleteTime').save(session_=notebooks_session.session)
    notebook.auto_delete_time = body.auto_delete_time
    return GenericSingleResponse[Notebook](data=notebook)


@router.post(
    "/namespaces/{namespace}/notebooks/add_port",
    response_model=GenericSingleResponse[AicpServers],
    status_code=http_status.HTTP_200_OK,
    summary="新增一个notebook对外端口",
    dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.WRITE)),
                  Depends(has_notebook_resource_permission_namespace("uuid"))]
)
def add_notebook_port(
        body: NoteBookPortParam,
        namespace: str = Depends(notebook_namespace_permission(permission_level=PermissionLevel.WRITE)),
        notebooks_session: NotebookCRUD = Depends(get_notebooks_crud)
):
    """
    新增一个notebook对外端口
    """
    notebook = Notebook.one_by_id(body.uuid, session_=notebooks_session.session)
    namespace = notebook.namespace
    notebook_cr = NoteBookCrOperator(body.uuid, namespace, notebook.user_id)
    notebook_cr.add_node_port(body.port, body.protocol)
    node_service = NotebookServices.one_by_id(f"{body.uuid}-node-port")
    if node_service:
        node_service.delete()
    service = notebook_cr.get_node_port_service()
    notebook_server: AicpServers
    for port in service['spec']['ports']:
        if body.port == port['targetPort']:
            notebook_server = AicpServers(
                server_name="node_port", target_port=body.port,
                server_type="node_port", status=False,
                protocol=body.protocol,
                url=f"{app.settings.SSH_HOST}:{port['nodePort']}"
            )
            return GenericSingleResponse[AicpServers](data=notebook_server)
    return GenericSingleResponse[AicpServers](data=None)


@router.post(
    "/namespaces/{namespace}/notebooks/remove_port",
    response_model=GenericSingleResponse[Notebook],
    status_code=http_status.HTTP_200_OK,
    summary="删除一个notebook对外端口",
    dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.WRITE)),
                  Depends(has_notebook_resource_permission_namespace("uuid"))]
)
def remove_notebook_port(
        body: NoteBookPortParam,
        namespace: str = Depends(notebook_namespace_permission(permission_level=PermissionLevel.WRITE)),
        notebooks_session: NotebookCRUD = Depends(get_notebooks_crud)
):
    """
    设置删除容器实例时间
    """
    notebook = Notebook.one_by_id(body.uuid, session_=notebooks_session.session)
    namespace = notebook.namespace
    notebook_cr = NoteBookCrOperator(body.uuid, namespace, notebook.user_id)
    notebook_cr.remove_node_port(body.port)
    node_service = NotebookServices.one_by_id(f"{body.uuid}-node-port")
    if node_service:
        node_service.delete()
    return GenericSingleResponse[Notebook](data=notebook)


@router.post(
    "/namespaces/{namespace}/notebooks/mounts/remove",
    response_model=GenericSingleResponse[Notebook],
    status_code=http_status.HTTP_200_OK,
    summary="删除一个notebook挂载点",
    dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.WRITE)),
                  Depends(has_notebook_resource_permission_namespace("uuid"))]
)
def remove_mount(
        request: Request,
        body: NoteBookMountsParam,
        namespace: str = Depends(notebook_namespace_permission(permission_level=PermissionLevel.WRITE)),
        notebooks_session: NotebookCRUD = Depends(get_notebooks_crud)
):
    """
    删除一个notebook挂载
    """
    notebook = Notebook.one_by_id(body.uuid, session_=notebooks_session.session)
    if notebook.status != NotebookStatus.Suspended:
        raise NotebookNotStoppedException()
    OperationRecord.create_by_resource(notebook, action="RemoveMount", status='RemoveMount',
                                       reason='RemoveMount', params=json.dumps(body.dict())) \
        .save(session_=notebooks_session.session)

    for volume in notebook.volume_specs:
        if volume.file_set == body.file_set and body.volume_type == volume.volume_type:
            vm = VolumesManager(request.user, notebook.namespace, volume)
            notebook_cr = NoteBookCrOperator(body.uuid, namespace, notebook.user_id)
            notebook_cr.remove_mount(vm)
            notebooks_session.session.delete(volume)
            break
    else:
        raise VolumeNotExistsException()

    return GenericSingleResponse[Notebook](data=notebook)


@router.post(
    "/namespaces/{namespace}/notebooks/mounts/add",
    response_model=GenericSingleResponse[Notebook],
    status_code=http_status.HTTP_200_OK,
    summary="添加一个notebook挂载",
    dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.WRITE)),
                  Depends(has_notebook_resource_permission_namespace("uuid"))]
)
def add_mount(
        request: Request,
        body: NoteBookMountsParam,
        namespace: str = Depends(notebook_namespace_permission(permission_level=PermissionLevel.WRITE)),
        notebooks_session: NotebookCRUD = Depends(get_notebooks_crud)
):
    """
    添加一个notebook挂载
    """
    notebook = Notebook.one_by_id(body.uuid, session_=notebooks_session.session)
    if notebook.status != NotebookStatus.Suspended:
        raise NotebookNotStoppedException()

    for volume in notebook.volume_specs:
        if volume.file_set == body.file_set and body.volume_type == volume.volume_type:
            raise VolumeExistsException()

    vs: NotebookVolumeSpec = NotebookVolumeSpec(**body.dict(exclude={"uuid"}))
    vm = VolumesManager(request.user, namespace, vs)
    vm.create_storage()
    vs.pvc_name = vm.volume.pvc_name
    notebook.volume_specs.append(vs)
    notebook_cr = NoteBookCrOperator(body.uuid, namespace, notebook.user_id)
    notebook_cr.add_mount(vm)

    return GenericSingleResponse[Notebook](data=notebook)


@router.post(
    "/namespaces/{namespace}/notebooks/change_resource_group",
    response_model=GenericSingleResponse[Notebook],
    status_code=http_status.HTTP_200_OK,
    summary="修改notebook资源组",
    dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.WRITE)),
                  Depends(has_notebook_resource_permission_namespace("uuid"))]
)
def change_resource_group(
        body: NoteBookChangeResourceGroupParam,
        namespace: str = Depends(notebook_namespace_permission(permission_level=PermissionLevel.WRITE)),
        notebooks_session: NotebookCRUD = Depends(get_notebooks_crud)
):
    """
    修改notebook资源组
    """
    notebook: Notebook = Notebook.one_by_id(body.uuid, session_=notebooks_session.session)
    if not notebooks_session.check_all_suspended([notebook]):
        raise NotebookNotStoppedException()

    resource_node = ResourceGroupCrud(session=notebooks_session.session).get_rg_node_with_user_permission(
        user_id=notebook.user_id, rg_id=body.rg_id, rg_node_id=body.rg_node_id)

    if resource_node is None:
        raise ResourceGroupNodeNotFoundException()

    OperationRecord.create_by_resource(notebook, action="ChangeResourceGroup", status='ChangeResourceGroup',
                                       reason='ChangeResourceGroup', params=json.dumps(body.dict())) \
        .save(session_=notebooks_session.session)
    notebook_cr = NoteBookCrOperator(body.uuid, namespace, notebook.user_id)
    notebook_cr.change_resource_group(body.rg_id, body.rg_node_id)
    notebook.replica_specs.rg_id = body.rg_id
    notebook.replica_specs.specs = body.rg_node_id or ""
    return GenericSingleResponse[Notebook](data=notebook)


@router.post(
    "/namespaces/{namespace}/notebooks/change_image",
    response_model=GenericSingleResponse[Notebook],
    status_code=http_status.HTTP_200_OK,
    summary="修改容器实例镜像",
    dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.WRITE)),
                  Depends(has_notebook_resource_permission_namespace("uuid"))]
)
def change_image(
        body: NoteBookChangeImageParam,
        namespace: str = Depends(notebook_namespace_permission(permission_level=PermissionLevel.WRITE)),
        notebooks_session: NotebookCRUD = Depends(get_notebooks_crud)
):
    """
    设置删除容器实例时间
    """
    notebook: Notebook = Notebook.one_by_id(body.uuid, session_=notebooks_session.session)
    if not notebooks_session.check_all_suspended([notebook]):
        raise NotebookNotStoppedException()
    OperationRecord.create_by_resource(notebook, action="ChangeImage", status='ChangeImage',
                                       reason='ChangeImage', params=json.dumps(body.dict())) \
        .save(session_=notebooks_session.session)
    notebook_cr = NoteBookCrOperator(notebook.uuid, notebook.namespace, notebook.user_id)
    notebook_cr.change_image(body.image_type, body.image_url, body.secret)
    notebook.image = body.image
    notebook.image_type = body.image_type
    return GenericSingleResponse[Notebook](data=notebook)


@router.get(
    "/namespaces/{namespace}/notebooks/{uuid}/processes",
    response_model=GenericMultipleResponse[Dict],
    status_code=http_status.HTTP_200_OK,
    summary="获取共享GPU容器实例进程",
    dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.READ))]
)
async def get_notebooks_pod_processes(
        uuid: str,
        namespace: str = Depends(notebook_namespace_permission(permission_level=PermissionLevel.READ)),
        user_db: UserCRUD = Depends(get_user_crud)
):
    """
    获取共享GPU容器实例进程, 仅运行在共享GPU上的容器实例生效
    """
    try:
        pod = await AsyncPod.get(uuid + "-0", namespace=namespace)
        command = ["sh", "-c", "nvidia-smi --query-gpu=uuid --format=csv,noheader"]
        ex = await pod.exec(command, stdout=sys.stdout.buffer, stderr=sys.stderr.buffer, check=False)
        output = ex.stdout.decode().strip()
    except NotFoundError as e:
        logger.warning(f"未在该ns:{namespace}找到该pod:{uuid}")
        return GenericMultipleResponse[Dict](data=[], counts=0)
    except ExecError as e:
        if "non-zero" not in str(e):
            logger.warning(f"exec {uuid} error.")
            raise e
        else:
            output = ""
    if output == "":
        logger.warning("容器实例未分配GPU")
        return GenericMultipleResponse[Dict](data=[], counts=0)

    gpu_uuids = output.split('\n')
    prometheus_result = await PromethusAsyncClient().get_gpu_processes('|'.join(gpu_uuids))
    res_data = []
    for metric in prometheus_result:
        item = {
            "GPU": metric["metric"]["GPU"],
            "PID": metric["metric"]["PID"],
            "Type": metric["metric"]["Type"],
            "PodName": metric["metric"].get("PodName", ""),
            "Namespace": metric["metric"].get("Namespace", ""),
            "Memory": metric["value"][1],
            "Process": metric["metric"]["Process"]
        }
        res_data.append(item)
    # Add user information.
    namespaces = [item.get("Namespace") for item in res_data]
    users_info = user_db.get_user_by_namespaces(namespaces)
    users_info_map = {user_info["user_id"].lower(): user_info for user_info in users_info}
    for item in res_data:
        user_info = users_info_map.get(item.get("Namespace"))
        if user_info:
            item["UserId"] = user_info["user_id"]
            item["Email"] = user_info["email"]
        else:
            item["UserId"] = "Physical machine process"
            item["Email"] = ""
    # sort by gpu_index
    res_data_sorted = sorted(res_data, key=lambda x: int(x["GPU"]))
    return GenericMultipleResponse[Dict](data=res_data_sorted, counts=len(res_data))
