from __future__ import annotations
from typing import List, Optional

from pydantic import BaseModel, Field


class User(BaseModel):
    kind: str
    name: str


class RoleRef(BaseModel):
    api_group: str = Field(..., alias="apiGroup")
    kind: str
    name: str


class Binding(BaseModel):
    user: User
    referred_namespace: str = Field(..., alias="referredNamespace")
    role_ref: RoleRef = Field(..., alias="RoleRef")


class Bindings(BaseModel):
    bindings: Optional[List[Binding]]


class Namespace(BaseModel):
    user: str
    namespace: str
    namespace_user_id: str = ""
    role: str
    email: str


class WorkGroupInfo(BaseModel):
    user: str
    namespaces: List[Namespace]
    is_cluster_admin: bool = Field(..., alias="isClusterAdmin")
