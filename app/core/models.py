import json
import pickle
import time
import uuid
import uuid as uuid_pkg
from datetime import datetime
from enum import Enum
from functools import cached_property
from typing import Any, List, Optional, Set, Type, TypeVar, Union

import kr8s.objects
from pydantic import BaseModel, validator
from sqlalchemy import ARRAY, FetchedValue, Select, String, TIMESTAMP, column, func, text
from sqlalchemy.exc import IntegrityError, NoResultFound, OperationalError
from sqlalchemy.orm import validates
from sqlalchemy.orm.exc import FlushError
from sqlmodel import Field, SQLModel, Column, DateTime, Session, select
from sqlmodel.ext.asyncio.session import AsyncSession
from starlette.authentication import BaseUser
import app
from app import logger
from app.core.exceptions import ParameterException
from app.core.kube.api.dockercfg import DockerCfgSecret
from app.core.qingcloud.resource import ProductCenterResource
from app.core.utils import get_hash
from .constant import UserFrom
from .db import async_load_eager_fields, async_session_scope, load_eager_fields, session_scope
from .qingcloud.interface import describe_access_key_by_user_id, describe_user


class ActiveRecordMixin(SQLModel):
    __eager__fields__ = []

    @classmethod
    def primary_key(cls):
        return cls.__table__.primary_key.columns.keys()[0]

    @classmethod
    @session_scope
    @load_eager_fields
    def exec_by_stmt(cls, stmt, session_):
        return session_.exec(stmt).all()

    @classmethod
    @session_scope
    def count_over_all_by_stmt(cls, stmt: Select, session_: Session) -> int:
        count_stmt = stmt.with_only_columns(func.count().over().label("count"))
        return session_.exec(count_stmt).first() or 0

    @classmethod
    @session_scope
    @load_eager_fields
    def one_by_id(cls, id_, session_):
        obj = session_.get(cls, id_)
        return obj

    @classmethod
    @async_session_scope
    @async_load_eager_fields
    async def one_by_id_async(cls, id_, session_):
        return await session_.get(cls, id_)

    @classmethod
    @session_scope
    @load_eager_fields
    def all_by_ids(cls, ids_: List, session_):
        return session_.exec(
            select(cls).where(getattr(cls, cls.primary_key()).in_(ids_))
        ).all()

    @classmethod
    @async_session_scope
    @async_load_eager_fields
    async def all_by_ids_async(cls, ids_: List, session_):
        return (await session_.exec(
            select(cls).where(getattr(cls, cls.primary_key()).in_(ids_), )
        )).all()

    @classmethod
    @session_scope
    @load_eager_fields
    def all_by_ids_in_namespace(cls, ids_: List, namespace: str, session_):
        return session_.exec(
            select(cls).where(getattr(cls, cls.primary_key()).in_(ids_), cls.namespace == namespace)
        ).all()

    @classmethod
    @session_scope
    @load_eager_fields
    def all_by_ids_in_namespaces(cls, ids_: List, namespaces: List[str], session_):
        return session_.exec(
            select(cls).where(getattr(cls, cls.primary_key()).in_(ids_), cls.namespace.in_(namespaces))
        ).all()

    @classmethod
    @async_session_scope
    @async_load_eager_fields
    async def all_by_ids_in_namespace_async(cls, ids_: List, namespace: str, session_):
        return (await session_.exec(
            select(cls).where(getattr(cls, cls.primary_key()).in_(ids_), cls.namespace == namespace)
        )).all()

    @classmethod
    @async_session_scope
    @async_load_eager_fields
    async def all_by_ids_in_namespaces_async(cls, ids_: List, namespaces: List[str], session_):
        return (await session_.exec(
            select(cls).where(getattr(cls, cls.primary_key()).in_(ids_), cls.namespace.in_(namespaces))
        )).all()

    @classmethod
    @session_scope
    @load_eager_fields
    def first_by_fields(cls, fields: dict, session_):
        statement = select(cls)
        for key, value in fields.items():
            statement = statement.where(getattr(cls, key) == value)
        try:
            return session_.exec(statement).first()
        except NoResultFound:
            logger.error(f"{cls}: first_by_fields failed, NoResultFound")
            return None

    @classmethod
    @async_session_scope
    @async_load_eager_fields
    async def first_by_fields_async(cls, fields: dict, session_):
        statement = select(cls)
        for key, value in fields.items():
            statement = statement.where(getattr(cls, key) == value)
        try:
            return (await session_.exec(statement)).first()
        except NoResultFound:
            logger.error(f"{cls}: first_by_fields failed, NoResultFound")
            return None

    @classmethod
    @session_scope
    @load_eager_fields
    def first_by_field(cls, field: str, value: Any, session_):
        return cls.first_by_fields({field: value}, session_)

    @classmethod
    @async_session_scope
    @async_load_eager_fields
    def first_by_field_async(cls, field: str, value: Any, session_):
        return cls.first_by_fields_async({field: value}, session_, )

    @classmethod
    def one_by_field(cls, field: str, value: Any, session_):
        return cls.one_by_fields({field: value}, session_)

    @classmethod
    def one_by_fields(cls, fields: dict, session_=None):
        statement = select(cls)
        for key, value in fields.items():
            statement = statement.where(getattr(cls, key) == value)
        try:
            return session_.exec(statement).one()
        except NoResultFound:
            logger.error(f"{cls}: one_by_fields failed, NoResultFound")
            return None

    @classmethod
    def all_by_field(cls, field: str, value: Any, session_=None):
        statement = select(cls).where(getattr(cls, field) == value)
        return session_.exec(statement).all()

    @classmethod
    def all_by_fields(cls, fields: dict, session_=None):
        statement = select(cls)
        for key, value in fields.items():
            statement = statement.where(getattr(cls, key) == value)
        return session_.exec(statement).all()

    @classmethod
    def convert_without_saving(cls, source: Union[dict, SQLModel], update: Optional[dict] = None) -> SQLModel:
        if isinstance(source, SQLModel):
            obj = cls.from_orm(source, update=update)
        elif isinstance(source, dict):
            obj = cls.parse_obj(source, update=update)
        return obj

    @classmethod
    def create(cls, source: Union[dict, SQLModel], update: Optional[dict] = None, session_=None) -> Optional[
        SQLModel]:
        obj = cls.convert_without_saving(source, update)
        if obj is None:
            return None
        if obj.save(session_):
            return obj
        return None

    @classmethod
    def create_or_update(cls, source: Union[dict, SQLModel], update: Optional[dict] = None, session_=None) \
            -> Optional[SQLModel]:
        obj = cls.convert_without_saving(source, update)
        if obj is None:
            return None
        pk = cls.__mapper__.primary_key_from_instance(obj)
        if pk[0] is not None:
            existing = session_.get(cls, pk)
            if existing is None:
                return None  # Error
            else:
                existing.update(session_, obj)  # Update
                return existing
        else:
            return cls.create(session_, obj)  # Create

    @classmethod
    @session_scope
    def count(cls, session_=None) -> int:
        return session_.exec(select(func.count()).select_from(cls)).one()

    @classmethod
    @session_scope
    async def count_async(cls, session_=None) -> int:
        return (await session_.exec(select(func.count()).select_from(cls))).one()

    @session_scope
    @load_eager_fields
    def refresh(self, session_=None):
        session_.refresh(self)
        return self

    @async_session_scope
    @async_load_eager_fields
    async def refresh_async(self, session_=None):
        await session_.refresh(self)
        return self

    @session_scope
    def save(self, session_: Session) -> bool:
        try:
            session_.add(self)
            session_.commit()
            session_.refresh(self)
            return True
        except (IntegrityError, OperationalError, FlushError) as e:
            logger.error(e)
            session_.rollback()
            return False

    @async_session_scope
    async def save_async(self, session_: AsyncSession) -> bool:
        try:
            session_.add(self)
            await session_.commit()
            await session_.refresh(self)
            return True
        except (IntegrityError, OperationalError, FlushError) as e:
            logger.error(e)
            await session_.rollback()
            return False

    @classmethod
    @session_scope
    async def bulk_save(cls, objs: List[SQLModel], session_: Session):
        session_.add_all(objs)
        try:
            session_.commit()
            return True
        except (IntegrityError, OperationalError, FlushError) as e:
            logger.error(e)
            session_.rollback()
            return False

    @session_scope
    @load_eager_fields
    def update(self, session_: Session, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)
        self.save(session_=session_)
        return self

    @async_session_scope
    @load_eager_fields
    async def update_async(self, session_: AsyncSession, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)
        await self.save_async(session_=session_)
        return self

    @session_scope
    def delete(self, session_=None):
        session_.delete(self)
        session_.commit()

    @async_session_scope
    async def delete_async(self, session_=None):
        session_.delete(self)
        await session_.commit()

    @classmethod
    def all(cls, session_=None):
        return session_.exec(select(cls)).all()


class AicpUser(BaseUser):
    """

    """

    def __init__(self, user_id: str, user_name: str, email: str, role: str, user_from: UserFrom,
                 permissions: List) -> None:
        self.user_id = user_id
        self.user_name = user_name
        self.email = email
        self.user_from = user_from
        self.role = role

        self.phone = None
        self.root_user_id = None
        self.privilege = None
        self.access_key_id = None
        self.secret_access_key = None
        self.permissions = permissions

    def is_super_user(self):
        if self.role in {"global_admin", "console_admin"}:
            return True
        return False

    def is_subuser(self):
        logger.info(f"Checking AicpUser if user {self.user_id} is a subuser")
        return self.user_id and self.root_user_id and self.user_id != self.root_user_id

    def is_root_user(self):
        return not self.is_subuser()


class QingcloudUser(AicpUser):
    """ Sample API User that gives basic functionality """

    def __init__(self, user_id: str, user_name: str, role: str, email: str, phone: str, root_user_id: str,
                 privilege: int, access_key_id: str, secret_access_key: str, user_from: UserFrom = UserFrom.CONSOLE,
                 permissions: List = None, **kwargs):
        """ QingcloudUser Constructor

        Args:
            user_id (str): The user's ID
            user_name (str): The user's name
            role (str): The user's role
            email (str): The user's email
            phone (str): The user's phone number
            root_user_id (str): The user's root user ID
            privilege (int): The user's privilege level

        """
        super().__init__(user_id, user_name, email, role, user_from, permissions)
        self.phone = phone
        self.root_user_id = root_user_id
        self.privilege = privilege
        self.access_key_id = access_key_id
        self.secret_access_key = secret_access_key

    def is_kse_admin_user(self):
        return self.user_from == UserFrom.KSE and self.role in {'global_admin'}

    def is_super_user(self):
        return self.user_from == UserFrom.KSE and self.role in {'global_admin'}

    def is_subuser(self):
        logger.info(f"Checking QingcloudUser if user {self.user_id} is a subuser")
        return self.user_id != self.root_user_id

    def __repr__(self):
        return f'<QingcloudUser {self.user_name} ({self.user_id})>'

    def __str__(self):
        return self.__repr__()

    @property
    def is_authenticated(self) -> bool:
        """ Checks if the user is authenticated. This method essentially does nothing, but it could implement session logic for example.

        Returns:
            bool: True if the user is authenticated
        """
        return True

    @property
    def display_name(self) -> str:
        """ Display name of the user """
        return f'{self.user_name} : {self.user_id}'

    @property
    def identity(self) -> str:
        """ Identification attribute of the user """
        return self.user_id


class HealthCheck(BaseModel):
    name: str
    version: str
    description: str


class StatusMessage(BaseModel):
    status: bool
    message: str


class AutomaticIdModel(SQLModel):
    id: Optional[int] = Field(default=None, primary_key=True)


class UUIDModel(SQLModel):
    uuid: uuid_pkg.UUID = Field(
        default_factory=uuid_pkg.uuid4,
        primary_key=True,
        index=True,
        nullable=False,
        sa_column_kwargs={
            "server_default": text("gen_random_uuid()"),
            "unique": True
        }
    )


class KubernetesObjectUUIDModel(SQLModel):
    kuid: uuid_pkg.UUID = Field(
        None,
        index=True,
        nullable=True,
        description="Kubernetes Object UUID, 用于标识Kubernetes Object"
    )


class NameSpaceModel(SQLModel):
    namespace: str = Field(..., nullable=False, index=True, description="命名空间")


class CustomImageSecret(SQLModel):
    has_auth: bool = Field(False, description="是否需要认证")
    username: str = Field("", description="镜像仓库用户名")
    password: str = Field("", description="镜像仓库密码")


class UserIdModel(SQLModel):
    user_id: str = Field(..., nullable=False, index=True)
    root_user_id: str = Field("", nullable=True, index=True)

    def get_user(self) -> QingcloudUser:
        return QingcloudUser(
            **{
                **describe_user(self.user_id).get("user_set")[0],
                **describe_access_key_by_user_id(self.user_id).get("access_key_set")[0]
            },
            user_from=UserFrom.CONSOLE)

    def root_user(self) -> QingcloudUser:
        return QingcloudUser(
            **{
                **describe_user(self.root_user_id).get("user_set")[0],
                **describe_access_key_by_user_id(self.root_user_id).get("access_key_set")[0]
            },
            user_from=UserFrom.CONSOLE)


class UserPatchInfoModel(SQLModel):
    user_name: str = Field("", nullable=False, description="用户名")
    email: str = Field("", nullable=False, description="邮箱")


class ImageModel(SQLModel):
    image: str = Field(..., nullable=False, description="镜像名称或者镜像地址")
    image_type: str = Field(..., nullable=False, description="镜像类型: official, user, custom, hpc, application, share")

    @property
    def image_url(self) -> str:
        """
        获取镜像地址
        :return:
        """
        value = self.image.strip()
        if value.startswith("docker "):
            value = value[7:].strip()
        if value.startswith("pull "):
            value = value[5:].strip()
        if " " in value:
            raise ParameterException("镜像名称不能包含空格")
        return value

    def create_secret(self, namespace: str, docker_secret: CustomImageSecret = None) -> str:
        """
        创建镜像认证secret
        :param namespace:
        :param username:
        :param password:
        :return:
        """
        if self.image_type in ["official", "user","application","hpc","share"]:
            docker_secret = DockerCfgSecret(namespace, app.settings.DOCKER_REGISTRY,
                                            app.settings.DOCKER_ADMIN_USER, app.settings.DOCKER_ADMIN_PASSWORD)
            docker_secret.create()
            docker_secret_name = docker_secret.name
        elif docker_secret and docker_secret.has_auth:
            docker_secret = DockerCfgSecret(namespace, self.image.split("/")[0],
                                            docker_secret.username, docker_secret.password)
            docker_secret.create()
            docker_secret_name = docker_secret.name
        else:
            docker_secret_name = None
        return docker_secret_name


class TimestampModel(SQLModel):
    created_at: datetime = Field(
        nullable=False,
        sa_column_kwargs={
            "server_default": text("current_timestamp(0)")
        }
    )

    updated_at: datetime = Field(
        nullable=False,
        sa_column_kwargs={
            "server_default": text("current_timestamp(0)"),
            "onupdate": text("current_timestamp(0)")
        }
    )


class EnvModel(SQLModel):
    name: str = Field(..., nullable=False)
    value: str = Field(..., nullable=False)


class StatusModel(SQLModel):
    status: str = Field(default=None, nullable=True)
    reason: str = Field(default=None, nullable=True, description="状态原因")

    def check_patch_status(self, new_status: str):
        """
        检查状态是否可以被修改
        :param new_status:
        :return:
        """
        created_status = [STATUS_PHASE.Pending, STATUS_PHASE.Creating, STATUS_PHASE.Created, STATUS_PHASE.Inqueuing]
        running_status = [STATUS_PHASE.Running]
        suspended_status = [STATUS_PHASE.Suspended]
        terminates_status = [STATUS_PHASE.Terminated]

        if new_status in terminates_status:
            return

        if self.status in created_status:
            return

        if self.status in running_status and new_status in suspended_status + terminates_status:
            return

        if self.status == suspended_status and new_status == running_status + terminates_status:
            return

        raise ParameterException(f"状态不允许从{self.status}变更为{new_status}")


class STATUS_PHASE:
    """
    Different values that the status phase should have. The frontend will be
    expecting only these values.
    """
    #
    # READY = "ready"
    # WAITING = "waiting"
    # ERROR = "error"
    # UNINITIALIZED = "uninitialized"
    # UNAVAILABLE = "unavailable"
    # TERMINATING = "terminating"
    # TERMINATED = "terminated"
    # STOPPED = "stopped"

    Warning = "warning"
    Pending = "Pending"
    Creating = "Creating"
    Created = "Created"
    Inqueuing = "Inqueuing"
    Running = "Running"
    Succeeded = "Succeeded"
    Failed = "Failed"
    Suspending = "Suspending"
    Suspended = "Suspended"
    Terminating = "Terminating"
    Terminated = "Terminated"
    Deleted = "Deleted"
    Unknown = "Unknown"
    Unavailable = "Unavailable"


class DeletionModel(SQLModel):
    deleted: bool = Field(default=False, nullable=False, description="是否删除")
    deleted_at: datetime = Field(
        nullable=True,
        sa_column_kwargs={
            "server_default": FetchedValue()
        }
    )


M = TypeVar("M", bound=BaseModel)


class CommonSearchQueryParams:
    def __init__(self, offset: int = 0, limit: int = 10):
        self.offset = offset
        self.limit = limit


class ReplicaSpecBase(SQLModel, table=False):
    replicas: int = Field(1, description="副本数量, 默认为1")
    specs: str = Field(..., nullable=False,
                       description="副本配置id, 从boss product center的sku_id, 或者节点id, 如果为节点id的情况下必选传入rg_id")
    replica_type: str = Field(..., nullable=False,
                              description="副本类型: \n"
                                          "Tfjobs : Master, Worker, PS, Chief, Evalator\n"
                                          "Pytorchjobs : Master, Worker, PS, Chief, Evalator\n"
                                          "MPIjobs : Master, Worker, PS, Chief, Evalator\n"
                                          "Notebook : Master\n")
    rg_id: str = Field(None, nullable=True, description="资源组ID")

    # if specs is exclusive group, then custom specs is allowed
    # if specs is not exclusive group, then custom specs will autofill with specs
    custom_cpu: int = Field(None, nullable=True, description="cpu核数")
    custom_memory: int = Field(None, nullable=True, description="内存大小, 单位MB")
    custom_gpu: int = Field(0, nullable=True, description="gpu数量")
    custom_gpu_list: Optional[Set[str]] = Field(default=None, sa_column=Column(ARRAY(String())),
                                                description="gpu列表, 传入gpu在节点的index值, 比如 [0, 1, 2]")
    custom_gpu_name: Optional[str] = Field("", nullable=True, description="gpu名称")
    custom_gpu_memory: str = Field("", nullable=True, description="gpu内存大小")
    custom_gpu_type: str = Field("", nullable=True, description="gpu类型")
    custom_system_disk_size: int = Field(0, nullable=True, description="系统盘大小, 单位GB")
    custom_data_disk_size: int = Field(0, nullable=True, description="数据盘大小, 单位GB")
    custom_infiniband: Optional[int] = Field(0, nullable=True, description="infiniband数量")
    custom_aipods_type: Optional[str] = Field("", nullable=True, description="aipod类型")
    custom_hashrate_allocation: Optional[int] = Field(0, nullable=True, description="算力分配")
    custom_network_description: Optional[str] = Field("", nullable=True, description="网络描述")
    custom_cpu_model: Optional[str] = Field("", nullable=True, description="cpu型号")
    custom_cpu_manufacturer: Optional[str] = Field("", nullable=True, description="cpu制造商")

    def get_summary(self) -> str:
        """
        获取副本配置摘要
        {cpu}c{mem}Gi{Gpu_name}
        :return:
        """
        cpu = f"{self.custom_cpu}核"
        mem = f"/{self.custom_memory}Gi"
        gpu = f"/{self.custom_gpu_name} * {self.custom_gpu}" if self.custom_gpu_name and self.custom_gpu else ""

        return f"{cpu}{mem}{gpu}"

    @cached_property
    def resource(self) -> ProductCenterResource:
        """
        获取资源配置
        :return:
        """
        init_properties_flag = True if self.id is None else False
        return ProductCenterResource(self.specs, self.replicas, self.rg_id, custom_properties=self.dict(),
                                     init_properties=init_properties_flag)

    def update_custom_specs(self):
        """
        更新custom_specs
        :return:
        """
        # 如果已经有了rg_id, 则不需要更新custom_specs
        if self.rg_id:
            return
        self.custom_aipods_type = self.resource.aipods_type.value if self.resource.aipods_type else ""
        self.custom_cpu = self.resource.cpu_count.value
        self.custom_memory = self.resource.memory.value
        self.custom_gpu = self.resource.gpu_count.value if self.resource.gpu_count else 0
        self.custom_gpu_memory = self.resource.gpu_memory.name.rstrip("G") if self.resource.gpu_memory else ""
        self.custom_gpu_type = self.resource.gpu_model.value if self.resource.gpu_model else ""
        self.custom_gpu_name = self.resource.gpu_model.name if self.resource.gpu_model else ""
        self.custom_system_disk_size = self.resource.os_disk.value if self.resource.os_disk else 0
        self.custom_data_disk_size = self.resource.disk.value if self.resource.disk else 0
        self.custom_infiniband = self.resource.network.value if self.resource.network else 0
        self.custom_hashrate_allocation = self.resource.hashrate_allocation.value if self.resource.hashrate_allocation else 0
        self.custom_network_description = self.resource.network_description.value if self.resource.network_description else ""
        self.custom_cpu_model = self.resource.cpu_model.value if self.resource.cpu_model else ""
        self.custom_cpu_manufacturer = self.resource.cpu_manufacturer.value if self.resource.cpu_manufacturer else ""

    def rg_custom_to_sku_dict(self):
        """
        custom to sku dict
        :return:
        """
        return {
            "sku_id": self.specs,
            "filters": [
                {
                    "attr_id": "cpu_count",
                    "attr_value": self.custom_cpu
                },
                {
                    "attr_id": "memory",
                    "attr_value": self.custom_memory
                },
                {
                    "attr_id": "gpu_model",
                    "attr_value": self.custom_gpu_type
                },
                {
                    "attr_id": "aipods_type",
                    "attr_value": self.rg_id
                },
                {
                    "attr_id": "gpu_count",
                    "attr_value": self.custom_gpu
                },
                {
                    "attr_id": "gpu_memory",
                    "attr_value": self.custom_gpu_memory
                },
                {
                    "attr_id": "resource_group",
                    "attr_value": self.rg_id
                },
                {
                    "attr_id": "resource_group_node",
                    "attr_value": self.specs
                },
                {
                    "attr_id": "aipods_scope",
                    "attr_value": "resource_group"
                },
                {
                    "attr_id": "cpu_model",
                    "attr_value": self.custom_cpu_model
                },
                {
                    "attr_id": "cpu_manufacturer",
                    "attr_value": self.custom_cpu_manufacturer
                }
            ]
        }

    class Config:
        keep_untouched = (cached_property,)


class ReplicaSpecCreate(ReplicaSpecBase, table=False):
    template_id: Optional[int] = Field(None, nullable=True, description="资源模板ID")

    class Config:
        schema_extra = {
            "example": {
                "replicas": 1,
                "specs": "sku-1",
                "replica_type": "Master",
                "rg_id": "rg-1",
                "custom_cpu": 1,
                "custom_memory": 2,
                "custom_gpu": 0,
            }
        }


class VolumeSpecBase(SQLModel, table=False):
    file_set: str = Field(..., nullable=False, description="文件集名称")
    mount_path: str = Field(..., nullable=False, description="挂载路径")
    volume_type: str = Field(..., nullable=False, description="挂载类型: GPFS")
    file_type: str = Field("Directory", nullable=True, description="文件类型: Directory, File, Directory",
                           sa_column_kwargs={"server_default": "Directory"})
    quota: int = Field(0, description="配额大小, 单位GB")
    permission: str = Field("rw", description="权限, rw, ro", sa_column_kwargs={"server_default": "rw"})
    owner: str = Field("", description="存储拥有者", sa_column_kwargs={"server_default": ""})


class VolumeSpecCreate(VolumeSpecBase, table=False):
    class Config:
        schema_extra = {
            "example": ""
        }


class BillingBase(SQLModel):
    charge_mode: Optional[str] = Field(None, nullable=True,
                                       description="计费模式, 弹性计费或者包年包月, elastic, monthly")
    duration: Optional[int] = Field(None, nullable=True, description="租赁时长, 弹性为3600, 包年包月为1-60")
    auto_renew: Optional[int] = Field(None, nullable=True, description="是否自动续费, 0为不自动续费, 1为自动续费")
    next_charge_mode: Optional[str] = Field(None, nullable=True,
                                            description="下次计费模式, 用于变更计费模式, elastic, monthly")


class TaskInfo(SQLModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), nullable=False, primary_key=True, description="任务ID")
    name: str = Field(..., nullable=False, description="任务名称")
    description: str = Field("", nullable=True, description="任务描述")
    progress: int = Field(0, nullable=False, description="任务进度")
    finished: bool = Field(False, nullable=False, description="任务是否完成")
    # Running / Success / Failed
    status: str = Field("Running", nullable=False, description="任务状态")
    result: dict = Field({}, nullable=True, description="任务结果")
    create_at: int = Field(default_factory=lambda: int(time.time()), nullable=True, description="任务创建时间")
    update_at: int = Field(default_factory=lambda: int(time.time()), nullable=True, description="任务更新时间")

    def dumps(self):
        return self.json()

    @classmethod
    def loads(cls, data):
        """

        :param data:  a json string
        :return:
        """
        return cls.parse_obj(json.loads(data))


class ServiceType(str, Enum):
    jupyter = "jupyter"
    vscode = "vscode"
    ssh = "ssh"
    custom = "custom"


class AicpServers(BaseModel):
    url: str = Field(..., nullable=False, description="服务地址")
    target_port: int = Field(..., nullable=False, description="服务端口")
    server_type: str = Field(..., nullable=False, description="服务类型")
    server_name: str = Field(..., nullable=False, description="服务名称")
    status: bool = Field(False, nullable=False, description="服务状态")
    protocol: str = Field("http", nullable=True, description="服务协议", sa_column_kwargs={"server_default": "http"})
    password: Optional[str] = Field(None, nullable=True, description="服务密码")
