import json
from typing import Generic, Optional
import typing

from pydantic import BaseModel, validator
from pydantic.generics import GenericModel
from sqlmodel import Field
from starlette.responses import JSONResponse

from app.core.config import get_request_id_context_value, get_user_id_context_value, request_id_context, user_id_context
from app.core.models import M


class AICPJSONResponse(JSONResponse):

    def render(self, content: typing.Any) -> bytes:
        content.update({
            "meta": {
                "X_REQUEST_ID": get_request_id_context_value(),
                "X_REQUEST_USER": get_user_id_context_value()
            }
        })
        return json.dumps(
            content,
            ensure_ascii=False,
            allow_nan=False,
            indent=None,
            separators=(",", ":"),
        ).encode("utf-8")


class ResponseMetaData(BaseModel):
    X_REQUEST_ID: Optional[str] = Field(default_factory=get_request_id_context_value, nullable=True)
    X_REQUEST_USER: Optional[str] = Field(default_factory=get_user_id_context_value, nullable=True)


class BaseGenericResponse(GenericModel):
    ret_code: int = 0
    message: str = "success"

    meta: ResponseMetaData = Field(
        default_factory=lambda: ResponseMetaData(), nullable=True
    )

    def to_text(self):
        return json.dumps(self.dict(), ensure_ascii=False, default=str)


class BaseErrorResponse(BaseGenericResponse):
    ret_code: int = 5000
    message: str = "internal server error, please contact the administrator."


class GenericSingleResponse(BaseGenericResponse, Generic[M]):
    data: M


class GenericMultipleResponse(BaseGenericResponse, Generic[M]):
    data: list[M]
    counts: int = 0
