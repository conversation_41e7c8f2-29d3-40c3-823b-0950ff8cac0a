from datetime import datetime
from typing import Any

from kubernetes import client
from sqlmodel import Session
from sqlalchemy import func, and_, desc, or_, asc

from app import logger
from app.models.gpu import GpuMaintainLog, NodeStaticInfo, GpuStaticInfo, GpuFaultRecords, \
    GpuErrorCodes, IbDevInfo, GpuDashboardConfig
from app.apps.gpu.utils import set_k8s_node_scheduling
from app.models.resource_group import ResourceNodeStatus
from app.models.resource_pool import ResourcePool
from app.models.user import UserInfo
from app.common.response import GPU_RESOURCE_NOT_FOUND, ERROR_GPU_FAULT_RECORDS_NOT_FOUND
from app.core.kube.api.events import create_event
from app.core.response import BaseGenericResponse

from app.core.utils import generate_random_string


class GpuCrud:
    """
    GpuCrud CRUD
    """

    def __init__(self, session: Session):
        self.session: Session = session

    def list_user_info(self, daily: str = None, count: str = None):
        query = self.session.query(UserInfo)
        if daily == "today":
            query = query.filter(func.date(UserInfo.created_at) == datetime.now().date())
        if daily == "all":
            if count is not None:
                return query.count()
        return query.order_by(desc(UserInfo.created_at)).all()

    def list_gpu_dashboard_config(self):
        return self.session.query(GpuDashboardConfig).order_by(asc(GpuDashboardConfig.dashboard_id)).all()

    def update_gpu_dashboard_config(self, dashboard_id: str, dashboard_name: str = None, enable_dashboard: str = None,
                                    enable_table_view: str = None, grafana_address: str = None, uid: str = None,
                                    template: str = None, grafana_params: str = None):
        gpu_dashboard_config = self.session.query(GpuDashboardConfig).filter_by(dashboard_id=dashboard_id).first()
        if gpu_dashboard_config:
            gpu_dashboard_config.dashboard_id = dashboard_id
            if grafana_address is not None:
                gpu_dashboard_config.grafana_address = grafana_address
            if dashboard_name is not None:
                gpu_dashboard_config.dashboard_name = dashboard_name
            if enable_dashboard is not None:
                gpu_dashboard_config.enable_dashboard = enable_dashboard
            if enable_table_view is not None:
                gpu_dashboard_config.enable_table_view = enable_table_view
            if uid is not None:
                gpu_dashboard_config.dashboard_url_path = uid
            if template is not None:
                gpu_dashboard_config.web_router = template
            if grafana_params is not None:
                gpu_dashboard_config.grafana_params = grafana_params
            self.session.commit()
        else:
            rep = BaseGenericResponse()
            rep.ret_code = GPU_RESOURCE_NOT_FOUND
            rep.message = ERROR_GPU_FAULT_RECORDS_NOT_FOUND
            return rep
        return gpu_dashboard_config

    def get_resource_pool(self, pool_id: str):
        return self.session.query(ResourcePool).filter_by(pool_id=pool_id).first()

    def get_ml(self, ml_id: str):
        get_gpu_maintain_log = self.session.get(GpuMaintainLog, ml_id)
        logger.info("get GpuMaintainLog [%s]", get_gpu_maintain_log)
        return get_gpu_maintain_log

    def create_gpu_maintain_log(self, gpu_node_id: str, description: str) -> Any:
        gpu_maintain_log = GpuMaintainLog()
        gpu_maintain_log.ml_id = "gml-" + generate_random_string()
        gpu_maintain_log.maintainer_id = "admin"  # user.user_id
        gpu_maintain_log.maintainer_name = "admin"  # user.user_name
        gpu_maintain_log.gpu_node_id = gpu_node_id
        gpu_maintain_log.description = description
        self.session.add(gpu_maintain_log)
        self.session.commit()
        self.session.refresh(gpu_maintain_log)
        return gpu_maintain_log

    def get_gpu_node(self, gpu_node_id: str):
        result = self.session.query(ResourceNodeStatus).filter_by(
            node_id=gpu_node_id).count()
        return result

    def get_gpu_node_maintain_log(self, gpu_node_id: str, search_word=None):
        query = self.session.query(GpuMaintainLog).filter(GpuMaintainLog.gpu_node_id == gpu_node_id)
        if search_word is not None:
            query = query.filter(GpuMaintainLog.maintainer_name.like(f'%{search_word}%'))
        return query.all()

    def get_gpu_error_codes(self, search_word=None, gpu_product=None):
        query = self.session.query(GpuErrorCodes)
        if search_word is not None:
            query = query.filter(GpuErrorCodes.gpu_err_desc.like(f'%{search_word}%'))
        if gpu_product is not None:
            query = query.filter(GpuErrorCodes.gpu_product == gpu_product)
        return query.order_by(asc(GpuErrorCodes.gpu_err_priority)).all()

    def update_gpu_error_code(self, code_id: str, gpu_err_strategy: str, gpu_err_priority: str, gpu_err_desc: str,
                              gpu_suggestions: str):
        gpu_error_code = self.session.query(GpuErrorCodes).filter_by(code_id=code_id).first()
        if gpu_error_code:
            gpu_error_code.code_id = code_id
            gpu_error_code.gpu_err_strategy = gpu_err_strategy
            gpu_error_code.gpu_err_priority = gpu_err_priority
            gpu_error_code.gpu_err_desc = gpu_err_desc
            gpu_error_code.gpu_suggestions = gpu_suggestions
            self.session.commit()
        else:
            rep = BaseGenericResponse()
            rep.ret_code = GPU_RESOURCE_NOT_FOUND
            rep.message = ERROR_GPU_FAULT_RECORDS_NOT_FOUND
            return rep
        return gpu_error_code

    def get_gpu_status(self, search_word=None, worker_node_type=None, gpu_product=None, ib_enabled=None, limit=1000,
                       offset=0):
        query = self.session.query(ResourceNodeStatus.node_id, ResourceNodeStatus.status)
        if worker_node_type is not None:
            query = query.filter(ResourceNodeStatus.worker_node_type == worker_node_type)
        if ib_enabled is not None:
            query = query.filter(ResourceNodeStatus.ib_enabled == ib_enabled)
        if gpu_product is not None:
            query = query.filter(ResourceNodeStatus.gpu_product == gpu_product)
        if search_word is not None:
            query = query.filter(ResourceNodeStatus.node_id.like(f'%{search_word}%'))
        return query.offset(offset).limit(limit).all()

    def get_node_status(self):
        return self.session.query(ResourceNodeStatus.node_id, ResourceNodeStatus.status,
                                  ResourceNodeStatus.gpu_product, ResourceNodeStatus.total_gpu,
                                  ResourceNodeStatus.worker_node_type).all()

    def get_node_static_info(self, gpu_node_id: str, ib_count_compute: int = None):
        query = self.session.query(NodeStaticInfo)
        if gpu_node_id is not None:
            query = query.filter(NodeStaticInfo.hostname == gpu_node_id)
        if ib_count_compute is not None:
            query = query.filter(NodeStaticInfo.ib_count_compute > ib_count_compute)
        return query.first()

    def list_node_static_info(self):
        return self.session.query(ResourceNodeStatus.node_id, ResourceNodeStatus.status).all()

    def get_gpu_static_info(self, gpu_node_id: str = None, count: str = None):
        query = self.session.query(GpuStaticInfo)
        if gpu_node_id is not None:
            query = query.filter(GpuStaticInfo.hostname == gpu_node_id)
        if count is not None:
            return query.count()
        return query.all()

    def get_ib_dev_info(self, gpu_node_id: str):
        return self.session.query(IbDevInfo).filter(IbDevInfo.hostname == gpu_node_id).all()

    def get_gpu_error_code_by_xid(self, xid: str):
        return self.session.query(GpuErrorCodes).filter(GpuErrorCodes.gpu_xid == xid).first()

    def get_gpu_error_code_by_code_id(self, code_id: str):
        return self.session.query(GpuErrorCodes).filter(GpuErrorCodes.code_id == code_id).first()

    def update_gpu_fault_record(self, fault_record_id: str, fault_desc: str):
        gpu_fault_records = self.session.query(GpuFaultRecords).filter_by(records_id=fault_record_id,
                                                                          fault_status="0").first()
        if gpu_fault_records:
            gpu_fault_records.records_id = fault_record_id
            gpu_fault_records.fault_treatment = fault_desc
            gpu_fault_records.fault_status = "1"

            self.session.commit()

            # 移除污点并开启调度
            set_k8s_node_scheduling(gpu_fault_records.gpu_node_id, False, "remove")
        else:
            rep = BaseGenericResponse()
            rep.ret_code = GPU_RESOURCE_NOT_FOUND
            rep.message = ERROR_GPU_FAULT_RECORDS_NOT_FOUND
            return rep
        return gpu_fault_records

    def get_gpu_fault_record_by_node_id(self, gpu_node_id=None, fault_status=None, search_word=None):
        query = self.session.query(GpuFaultRecords)
        if gpu_node_id is not None:
            query = query.filter(GpuFaultRecords.gpu_node_id == gpu_node_id)
        if fault_status is not None:
            query = query.filter(GpuFaultRecords.fault_status == fault_status)
        if search_word is not None:
            query = query.filter(or_(GpuFaultRecords.gpu_xid.like(f'%{search_word}%'),
                                     GpuFaultRecords.gpu_uuid.like(f'%{search_word}%')))
        return query.order_by(desc(GpuFaultRecords.created_at)).all()

        #  updated_at = datetime.now()
        #  treatment = "该告警未能在错误表中找到匹配的处理方案，请根据 xid 或 error 在 nvidia 官网寻求方案"

    def create_gpu_fault_records(self, alert_data: any) -> Any:
        annotations = alert_data["annotations"]
        labels = alert_data["labels"]

        # 从 node status 获取 GPU node uid，用于发送事件
        resourceNodeStatus = self.session.query(ResourceNodeStatus).filter_by(
            node_id=labels["Hostname"]).first()

        node_uid = ""

        if resourceNodeStatus is not None:
            node_uid = getattr(resourceNodeStatus, "uid")
        logger.info("gpu uid: %s", node_uid)

        gpu_nvml_version = ""
        if labels.get("DCGM_FI_NVML_VERSION") is not None:
            gpu_nvml_version = labels["DCGM_FI_NVML_VERSION"]

        gpu_cuda_driver_version = ""
        if labels.get("DCGM_FI_CUDA_DRIVER_VERSION") is not None:
            gpu_cuda_driver_version = labels["DCGM_FI_CUDA_DRIVER_VERSION"]

        # 查询是否存在初始状态的故障通知记录，如果存在，就不记录在 fault 表中
        gpu_fault_record = self.session.query(GpuFaultRecords).filter(and_(GpuFaultRecords.gpu_xid == labels["xid"],
                                                                           GpuFaultRecords.gpu_node_id == labels[
                                                                               "Hostname"],
                                                                           GpuFaultRecords.gpu_uuid == labels["UUID"],
                                                                           GpuFaultRecords.fault_status == "0")).first()
        gpu_fault_records = GpuFaultRecords()
        if gpu_fault_record is None:
            gpu_fault_records.records_id = "fault-" + generate_random_string()
            gpu_fault_records.gpu_node_id = labels["Hostname"]
            gpu_fault_records.gpu_uuid = labels["UUID"]
            gpu_fault_records.gpu_xid = labels["xid"]
            gpu_fault_records.gpu_model_name = labels["modelName"]
            gpu_fault_records.gpu_nvml_version = gpu_nvml_version
            gpu_fault_records.gpu_cuda_driver_version = gpu_cuda_driver_version
            gpu_fault_records.gpu_device = labels["device"]
            gpu_fault_records.gpu_alert_summary = annotations["summary"]
            gpu_fault_records.fault_status = "0"
            gpu_fault_records.fault_treatment = "暂未开始处理"
            gpu_fault_records.fault_maintainer_id = "admin"
            gpu_fault_records.fault_maintainer_name = "admin"
            self.session.add(gpu_fault_records)
            self.session.commit()
            self.session.refresh(gpu_fault_records)

            #  保存故障记录后，触发故障策略
            gpu_error_code = self.session.query(GpuErrorCodes).filter(GpuErrorCodes.gpu_xid == labels["xid"]).first()
            if gpu_error_code is not None:
                gpu_err_strategy = getattr(gpu_error_code, "gpu_err_strategy")
                if gpu_err_strategy == "1":
                    set_k8s_node_scheduling(labels["Hostname"], True, "set")

            # 获取当前时间并转换为ISO格式
            current_time = datetime.now().astimezone().isoformat()
            metadata_name = labels["Hostname"] + generate_random_string()

            event = client.CoreV1Event(
                api_version='v1',
                kind='Event',
                metadata=client.V1ObjectMeta(name=metadata_name),
                involved_object=client.V1ObjectReference(
                    api_version='v1',
                    kind='Node',
                    name=labels["Hostname"],
                    uid=labels["Hostname"]
                ),
                reporting_component="",
                reporting_instance="",
                reason=annotations["summary"],
                message=annotations["message"],
                type='Warning',
                first_timestamp=current_time,
                last_timestamp=current_time,
                source=client.V1EventSource(component='aicp')
            )
            create_event('default', event)
        return gpu_fault_records
