from typing import List

from sqlmodel import Session

from app import logger
from app.models.gpu import Gpu<PERSON><PERSON><PERSON><PERSON>ords, GpuUnschedulableXids, NodeStaticInfo, \
    GpuStaticInfo
from app.models.resource_pool import ResourcePool, ResourcePoolCreateReq, ResourcePoolModifyReq
from app.common.response import INTERNAL_ERROR
from app.core.constant import AIPODS_TYPE, COMPUTE_GROUP
from app.core.kube.api import read_nodes, replace_node_tag
from app.core.middlewares.auth.qingcloud_auth import QingcloudUser
from app.core.response import BaseGenericResponse
from app.core.utils import generate_random_string


class ResourcePoolCrud:
    """
    Train CRUD
    """

    def __init__(self, session: Session, user: QingcloudUser = None):
        self.session: Session = session
        self.user = user

    def generate_id(self, exclude_ids=None):
        while True:
            resource_id = "pool-" + generate_random_string()
            logger.info("get resource_id [%s]", resource_id)
            resource = self.session.query(ResourcePool).filter_by(pool_id=resource_id).count()
            if resource:
                continue
            if exclude_ids and resource_id in exclude_ids:
                continue
            return resource_id

    def add(self, req: ResourcePoolCreateReq):
        pool_id = self.generate_id()
        node_count = 0
        gpu_count = 0
        if req.node:
            nodes = req.node
            for hostname in nodes:
                node_static_info = self.session.query(NodeStaticInfo).filter_by(hostname=hostname).one_or_none()
                if not node_static_info:
                    logger.error("no found node [%s]", hostname)
                    return None
                node = read_nodes(hostname)
                node_labels = node.metadata.labels
                node_labels[AIPODS_TYPE] = req.aipods_type
                node_labels[COMPUTE_GROUP] = pool_id
                if not replace_node_tag(hostname, node):
                    logger.critical("add node tag from k8s failed")
                    self.session.rollback()
                    return None
                node_static_info.compute_group = pool_id
                self.session.add(node_static_info)
                node_count = node_count + 1
                gpu_count = gpu_count + node_static_info.gpu
        resource_pool = ResourcePool(**req.dict())
        resource_pool.pool_id = pool_id
        resource_pool.node_count = node_count
        resource_pool.gpu_count = gpu_count
        self.session.add(resource_pool)
        self.session.commit()
        return pool_id

    def update(self, req: ResourcePoolModifyReq) -> ResourcePool:
        resource_pool: ResourcePool = self.session.query(
            ResourcePool).filter_by(pool_id=req.pool_id).one_or_none()
        logger.debug(resource_pool)
        if resource_pool:
            resource_pool.pool_name = req.pool_name
            resource_pool.description = req.description
            self.session.add(resource_pool)
        logger.debug(resource_pool)
        return resource_pool

    def list(self):
        return self.session.query(ResourcePool).all()

    def delete(self, pool_id):
        rep = BaseGenericResponse()
        node_static_info = self.session.query(NodeStaticInfo).filter_by(compute_group=pool_id).count()
        if node_static_info:
            rep.ret_code = -1
            rep.message = "请先移除资源池中的节点，然后再删除资源池"
            return rep
        resource_pool = self.session.query(ResourcePool).filter_by(pool_id=pool_id).one_or_none()
        if resource_pool:
            self.session.delete(resource_pool)
            self.session.commit()
        return rep

    def add_node(self, req):
        rep = BaseGenericResponse()
        pool_id = req.pool_id
        node_list = req.nodes
        for hostname in node_list:
            node_static_info = self.session.query(NodeStaticInfo).filter_by(hostname=hostname).one_or_none()
            node = read_nodes(hostname)
            node_labels = node.metadata.labels
            source_pool_id = node_static_info.compute_group
            if source_pool_id:
                # 先更新原资源池配置信息
                source_resource_pool = self.session.query(ResourcePool).filter_by(pool_id=source_pool_id).one_or_none()
                if source_resource_pool:
                    source_resource_pool.node_count = source_resource_pool.node_count - 1
                    source_resource_pool.gpu_count = source_resource_pool.gpu_count - node_static_info.gpu
                    self.session.add(source_resource_pool)
            resource_pool = self.session.query(ResourcePool).filter_by(pool_id=pool_id).one_or_none()
            resource_pool.node_count = resource_pool.node_count + 1
            resource_pool.gpu_count = resource_pool.gpu_count + node_static_info.gpu
            node_static_info.compute_group = pool_id
            self.session.add(node_static_info)
            self.session.add(resource_pool)
            node_labels[AIPODS_TYPE] = resource_pool.aipods_type
            node_labels[COMPUTE_GROUP] = pool_id
            if not replace_node_tag(hostname, node):
                logger.critical("remove node tag from k8s failed")
                self.session.rollback()
                rep.ret_code = INTERNAL_ERROR
                rep.message = "往资源池添加节点失败"
                return rep
        return rep

    def remove_node(self, req):
        rep = BaseGenericResponse()
        for hostname in req.nodes:
            node = read_nodes(hostname)
            node_labels = node.metadata.labels
            if AIPODS_TYPE in node_labels:
                del node_labels[AIPODS_TYPE]
            if COMPUTE_GROUP in node_labels:
                del node_labels[COMPUTE_GROUP]
            if not replace_node_tag(hostname, node):
                logger.critical("remove node tag from k8s failed")
                self.session.rollback()
                return False
            node_static_info = self.session.query(NodeStaticInfo).filter_by(hostname=hostname).one_or_none()
            pool_id = node_static_info.compute_group
            node_static_info.compute_group = ''
            self.session.add(node_static_info)
            resource_pool = self.session.query(ResourcePool).filter_by(pool_id=pool_id).one_or_none()
            if resource_pool:
                resource_pool.node_count = resource_pool.node_count - 1
                resource_pool.gpu_count = resource_pool.gpu_count - node_static_info.gpu
                self.session.add(resource_pool)
            self.session.commit()
        return rep

    def get_node_gpu_count(self, node_id):
        return self.session.query(GpuStaticInfo).filter_by(hostname=node_id).count()

    def get_node_gpu_count_by_names(self, nodes_name: List[str]) -> int:
        if not nodes_name:
            return 0
        return self.session.query(GpuStaticInfo).filter(GpuStaticInfo.hostname.in_(nodes_name)).count()

    def get_unschedulable_gpu_count(self, nodes_name: List[str]) -> int:
        if not nodes_name:
            return 0
        return self.session.query(GpuFaultRecords).filter(
            GpuFaultRecords.gpu_node_id.in_(nodes_name),
            GpuFaultRecords.gpu_xid.in_(GpuUnschedulableXids),
            GpuFaultRecords.fault_status=="0",
        ).count()
