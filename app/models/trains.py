import uuid
from datetime import datetime
from typing import Dict, List, Optional

from pydantic import BaseModel, validator
from sqlalchemy import <PERSON>umn, Enum, JSON, text
from sqlmodel import Field, Relationship, SQLModel

import app
from app.core.models import ActiveRecordMixin, AutomaticIdModel, BillingBase, CustomImageSecret, DeletionModel, \
    EnvModel, ImageModel, \
    KubernetesObjectUUIDModel, \
    NameSpaceModel, ReplicaSpecBase, ReplicaSpecCreate, StatusModel, \
    TimestampModel, UUIDModel, \
    UserIdModel, UserPatchInfoModel, VolumeSpecBase, VolumeSpecCreate
from app.core.qingcloud.billing import QAIBillingService
from app.core.qingcloud.exceptions import BillingServiceException
from app.core.utils import gpu_manager, id_worker
from app.models.finetuning import Finetuning


class TrainStatus(str, Enum):
    Pending = "Pending"
    Creating = "Creating"
    Created = "Created"
    Inqueuing = "Inqueuing"
    CreateFailed = "CreateFailed"
    Running = "Running"
    Succeeded = "Succeeded"
    Completed = "Completed"
    Failed = "Failed"
    Suspending = "Suspending"
    Suspended = "Suspended"
    Restarting = "Restarting"
    Terminating = "Terminating"
    Terminated = "Terminated"
    Deleted = "Deleted"
    Unknown = "Unknown"


train_status_zhCN = {
    TrainStatus.Pending: "等待中",
    TrainStatus.Creating: "创建中",
    TrainStatus.Created: "已创建",
    TrainStatus.Inqueuing: "排队中",
    TrainStatus.CreateFailed: "创建失败",
    TrainStatus.Running: "运行中",
    TrainStatus.Succeeded: "成功",
    TrainStatus.Completed: "已完成",
    TrainStatus.Failed: "失败",
    TrainStatus.Suspending: "暂停中",
    TrainStatus.Suspended: "暂停",
    TrainStatus.Restarting: "重启中",
    TrainStatus.Terminating: "终止中",
    TrainStatus.Terminated: "已终止",
    TrainStatus.Deleted: "已删除",
    TrainStatus.Unknown: "未知"
}


class TrainReason(str, Enum):
    waiting_for_scheduling = "等待调度中"
    suspend = "已暂停"
    suspending = "暂停中"
    terminating = "释放中"
    running = "运行中"
    restarting = "重启中"
    initializing = "资源初始化中"
    unschedulable = "当前规格资源不足"
    image_building = "镜像构建中"
    image_build_failed = "镜像构建失败"
    suspend_by_billing = "欠费暂停"
    suspend_by_user = "用户暂停"
    unknown = "未知"


ALL_SEARCHED_TRAIN_STATUS = [
    TrainStatus.Pending, TrainStatus.Creating, TrainStatus.Created, TrainStatus.Running, TrainStatus.Restarting,
    TrainStatus.Succeeded, TrainStatus.Failed, TrainStatus.Suspended, TrainStatus.Terminating,
    TrainStatus.Terminated, TrainStatus.Unknown, TrainStatus.CreateFailed, TrainStatus.Inqueuing
]

END_STATUS = [TrainStatus.Succeeded, TrainStatus.Failed, TrainStatus.Terminating, TrainStatus.Terminated,
              TrainStatus.Completed, TrainStatus.Deleted]

export_headers = [
    "任务ID", "资源组", "资源组ID", "项目类别", "任务名称", "任务状态", "计算配置", "节点数量", "优先级", "镜像",
    "创建时间", "开始运行时间", "完成时间", "提交者用户名", "提交者用户ID", "邮箱", "任务运行时长", "占用卡时", "cpu核时",
    "cpu平均利用率", "内存平均利用率", "GPU平均利用率", "GPU显存平均利用率"
]


class Log(SQLModel, table=False):
    timestamp: datetime = Field(None, nullable=True, description="时间戳", alias="time")
    stream: str = Field('stdout', nullable=True, description="日志流, stdout;stderr", alias="stream")
    message: str = Field(None, nullable=True, description="日志内容", alias="log")


class TrainReplicaSpec(ReplicaSpecBase, AutomaticIdModel, table=True):
    # resource: Dict = Field(..., sa_column=Column(JSON), description="资源配置")
    train_uuid: str = Field(..., nullable=False, description="作业UUID", foreign_key="train.uuid")
    train: Optional["Train"] = Relationship(back_populates="replica_specs")

    __tablename__ = "train_replica_spec"


class TrainReplicaSpecRead(TrainReplicaSpec, table=False):
    rg_name: Optional[str] = Field(None, nullable=True, description="资源组名称")


class TrainVolumeSpec(VolumeSpecBase, AutomaticIdModel, table=True):
    pvc_name: str = Field("", nullable=True, description="pvc名称, 不需要传入, 由系统自动生成")
    train_uuid: str = Field(..., nullable=False, description="作业UUID", foreign_key="train.uuid")
    train: Optional["Train"] = Relationship(back_populates="volume_specs")

    __tablename__ = "train_volume_spec"


class TrainBase(ImageModel, NameSpaceModel, UserIdModel, table=False):
    name: str = Field("训练任务", nullable=True, description="训练名称")
    endpoint: str = Field(..., nullable=False,
                          description="作业类型: tfjobs, pytorchjobs, mpijobs, xgboostjobs, mxjobs paddlejobs")
    command: str = Field(..., nullable=False, description="作业启动命令")
    priority: int = Field(3, nullable=False, description="作业优先级", sa_column_kwargs={
        "server_default": text("3")
    })

    restart_policy: str = Field("Never", sa_column_kwargs={"default": "Never"},
                                description="重启策略: Never, OnFailure, Always")
    backoff_limit: int = Field(0, nullable=False, description="作业失败后的重试次数, 默认为0")

    ttl_seconds_after_finished: int = Field(60, nullable=False, description="作业完成后保留时间, 默认为60s(1 min)")
    active_deadline_seconds: int = Field(84000, nullable=False, description="作业最长执行时间, 默认为84000s(24 hour)")

    envs: List[EnvModel] = Field([], sa_column=Column(JSON), description="环境变量列表")
    code_path_uuid: Optional[uuid.UUID] = Field(None, nullable=True, description="代码路径, 上传代码时获取的uuid")

    user_name: str = Field("", nullable=False, description="用户名")

    project_category: Optional[str] = Field("", nullable=True, description="作业项目类别",
                                            sa_column_kwargs={"server_default": text("''")})
    project_category_key: Optional[str] = Field("", nullable=True, description="作业项目类别, 注入env, 自动转换",
                                                sa_column_kwargs={"server_default": text("''")})
    job_type: Optional[str] = Field("AI", nullable=True, description="作业类型")

    # noinspection PyMethodParameters
    @validator("envs")
    def val_envs(cls, val: List[EnvModel]):
        """

        :param val:
        :return:
        """
        if not val:
            return []
        return [v.dict() for v in val]

    class Config:
        arbitrary_types_allowed = True


class TrainPatch(SQLModel, table=False):
    uuid: str = Field(..., nullable=False, description="作业UUID")
    name: str = Field(None, nullable=True, description="作业名称")
    project_category: str = Field(None, nullable=True, description="作业项目类别")
    status: str = Field(default=None, nullable=True)

    @validator("status")
    def val_status(cls, val: str):
        if val not in [TrainStatus.Suspended, TrainStatus.Running, TrainStatus.Terminated]:
            raise ValueError("作业状态不正确")
        return val

    class Config:
        schema_extra = {
            "example": {
                "user_id": "usr-jaswGjDn",
                "status": "Pending",
                "uuid": ""
            }
        }


TrainUUIDPrefix = "tn-"


class Train(TrainBase, TimestampModel, StatusModel, KubernetesObjectUUIDModel, ActiveRecordMixin, table=True):
    uuid: str = Field(
        default_factory=lambda: TrainUUIDPrefix + id_worker.get_hex_id(),
        primary_key=True,
        index=True,
        nullable=False,
        max_length=36,
    )

    running_at: datetime = Field(None, nullable=True, description="作业开始运行时间")
    ended_at: datetime = Field(None, nullable=True, description="作业结束时间")

    volume_specs: List[TrainVolumeSpec] = Relationship(back_populates="train")
    replica_specs: List[TrainReplicaSpec] = Relationship(back_populates="train")

    __tablename__ = "train"

    def get_gpu_vendor(self):
        """
        get gpu type
        :return:
        """
        if not self.replica_specs:
            return None
        if self.replica_specs[0].custom_aipods_type and self.replica_specs[0].custom_aipods_type.upper() == "VGPU":
            return "VGPU"
        if gpu := gpu_manager.get_gpu_matcher(self.replica_specs[0].custom_gpu_type):
            return gpu.vendor
        return None

    def set_volume_specs(self, user, volume_specs_create: List[VolumeSpecCreate]):
        pass

    def get_replica_specs_summary(self):
        return self.replica_specs[0].get_summary()

    @property
    def billing_enable(self):
        """

        :return:
        """
        return app.settings.billing_enable and self.replica_specs[0].rg_id is None \
            and self.replica_specs[0].specs and self.replica_specs[0].specs.startswith("sku_")

    def get_billing_price_info(self) -> Dict:
        """
        generate billing price info
        :return:
        """
        self.replica_specs[0].resource.init_properties()
        return self.replica_specs[0].resource.get_billing_price_info()

    def get_price(self, charge_mode: str, duration: int):
        """
        get price
        :param charge_mode:
        :param duration:
        :param user_id:
        """
        if self.billing_enable:
            return QAIBillingService().get_price(self.user_id, self.get_billing_price_info(),
                                                 charge_mode=charge_mode, duration=duration)
        return None

    def lease(self, charge_mode: str, duration: int, auto_renew: int = 0, next_charge_mode: str = None):
        """
        lease resource
        """
        if self.billing_enable:
            QAIBillingService().lease(self.uuid, self.user_id, self.get_billing_price_info(),
                                      charge_mode=charge_mode, duration=duration, check_resource_balance=True,
                                      auto_renew=auto_renew, next_charge_mode=next_charge_mode)

    def unlease(self):
        """

        :param resource_id:
        :param user_id:
        """
        if self.billing_enable:
            try:
                QAIBillingService().unlease(self.uuid, self.user_id)
            except BillingServiceException as e:
                if e.billing_code == 2100:
                    # 资源不存在, 直接返回
                    return
                raise e

    def suspend_billing(self):
        """
        suspend
        """
        if self.billing_enable:
            QAIBillingService().suspend(self.uuid, self.user_id)

    def restart_billing(self):
        """
        restart
        """
        if self.billing_enable:
            QAIBillingService().restart(self.uuid, self.user_id, count=self.replica_specs[0].replicas)

    def check_resource_balance(self):
        """
        check resource balance, when start notebook
        :return:
        """
        if self.billing_enable:
            return QAIBillingService().check_resource_balance(self.uuid, self.user_id, self.get_billing_price_info())
        return True


class TrainRead(Train, UserPatchInfoModel, table=False):
    train_volume_specs: Optional[List[TrainVolumeSpec]] = Field([], description="挂载配置列表", sa_type=JSON,
                                                                alias="volume_specs")
    train_replica_specs: Optional[List[TrainReplicaSpecRead]] = Field([], description="", sa_type=JSON,
                                                                      alias="replica_specs")
    running_time: int = Field(None, nullable=True, description="作业运行时间, 单位: 秒")
    train_k8s_objects: Dict = Field(None, nullable=True, description="作业k8s原始数据", sa_type=JSON)
    gpu_card_time: int = Field(None, nullable=True, description="训练任务占用的GPU卡总时长, 单位: 秒")
    cpu_time: int = Field(None, nullable=True, description="训练任务占用的CPU总时长, 单位: 秒")
    finetuning: Optional[Finetuning] =  Field(None, nullable=True, description="微调参数", sa_type=JSON)


    @validator("train_volume_specs")
    def val_train_volume_specs(cls, v: Optional[List[TrainVolumeSpec]]):
        if not v:
            return v
        return list(filter(lambda x: x.volume_type == "GPFS", v))

    @classmethod
    def cal_gpu_cards(cls, replicas: List[TrainReplicaSpec]) -> int:
        return sum([x.custom_gpu * x.replicas for x in replicas if x.custom_gpu and x.replicas])

    @classmethod
    def cal_running_time(cls, running_at: datetime, ended_at: datetime) -> int:
        return int(((ended_at or datetime.now()) - running_at).total_seconds()) if running_at else 0

    @validator('running_time')
    def make_running_time(cls, v: int, values: dict) -> int:
        return cls.cal_running_time(values["running_at"], values["ended_at"])

    @validator('gpu_card_time')
    def make_gpu_card_time(cls, v: int, values: dict) -> Optional[int]:
        running_time = cls.cal_running_time(values["running_at"], values["ended_at"])
        gpu_cards = cls.cal_gpu_cards(values["train_replica_specs"])
        return running_time * gpu_cards

    @validator("cpu_time")
    def make_cpu_time(cls, v: int, values: dict) -> Optional[int]:
        running_time = cls.cal_running_time(values["running_at"], values["ended_at"])
        cpu_cards = sum([x.custom_cpu * x.replicas for x in values["train_replica_specs"] if x.custom_cpu and x.replicas])
        return running_time * cpu_cards

    def get_gpu_card_time(self):
        running_time = self.cal_running_time(self.running_at, self.ended_at)
        gpu_cards = self.cal_gpu_cards(self.train_replica_specs)
        return running_time * gpu_cards

    def get_cpu_time(self):
        running_time = self.cal_running_time(self.running_at, self.ended_at)
        cpu_cards = sum([x.custom_cpu * x.replicas for x in self.train_replica_specs if x.custom_cpu and x.replicas])
        return running_time * cpu_cards

    def get_running_time(self):
        return self.cal_running_time(self.running_at, self.ended_at)

    def get_gpu_vendor(self):
        """
        get gpu type
        :return:
        """
        if not self.train_replica_specs:
            return None
        if self.train_replica_specs[0].custom_aipods_type \
                and self.train_replica_specs[0].custom_aipods_type.upper() == "VGPU":
            return "VGPU"
        if gpu := gpu_manager.get_gpu_matcher(self.train_replica_specs[0].custom_gpu_type):
            return gpu.vendor
        return None

    def get_replica_specs_summary(self):
        return self.train_replica_specs[0].get_summary()

    class Config:
        arbitrary_types_allowed = True

        schema_extra = {
            "example": {
                "kuid": "48b20965-36e3-4b40-b910-a3df326539d2",
                "status": "Pending",
                "user_id": "usr-jaswGjDn",
                "created_at": "2023-12-26T10:17:33.150323",
                "updated_at": "2023-12-26T10:17:33.150323",
                "uuid": "eb1bc81c-a656-470d-85a7-13e1b25eb7f7",
                "name": "tf-job-example",
                "endpoint": "tfjobs",
                "namespace": "usr-jaswgjdn",
                "command": "python /var/tf_mnist/mnist_with_summaries.py",
                "image": "kubeflow/tf-mnist-with-summaries:latest",
                "image_type": "user",
                "restart_policy": "Never",
                "ttl_seconds_after_finished": 0,
                "replica_specs": [
                    {
                        "replicas": 2,
                        "specs": "",
                        "replica_type": "Worker"
                    }
                ],
                "envs": [
                    {
                        "name": "env1",
                        "value": "value1"
                    }
                ]
            }
        }


# class TrainReadWithK8sObject(TrainRead, table=False):
# train_k8s_objects: Dict = Field({}, description="作业k8s原始数据", sa_type=JSON)


class TrainCreate(TrainBase, BillingBase, table=False):
    replica_specs: List[ReplicaSpecCreate] = Field(..., description="副本配置列表")
    volume_specs: Optional[List[VolumeSpecCreate]] = Field([], description="挂载配置列表")
    custom_image_secret: Optional[CustomImageSecret] = Field(CustomImageSecret(has_auth=False),
                                                             description="自定义镜像仓库认证信息")

    class Config:
        schema_extra = {
            "example": {
                "user_id": "usr-jaswGjDn",
                "name": "tf-job-example1",
                "endpoint": "tfjobs",
                "namespace": "usr-jaswgjdn",
                "command": "python /home/<USER>/tfjob.py",
                "image": "registry.cn-beijing.aliyuncs.com/danchey/tensorflow:1.0.1",
                "image_type": "user",
                "restart_policy": "Never",
                "ttl_seconds_after_finished": 3000,
                "replica_specs": [
                    {
                        "replicas": 1,
                        "specs": "sku_vGX4NGO7vOKk",
                        "replica_type": "Worker"
                    }
                ],
                "volume_specs": [
                ],
                "envs": [
                    {
                        "name": "env1",
                        "value": "value1"
                    }
                ],
                "custom_image_secret": {
                    "has_auth": True,
                    "username": "",
                    "password": ""
                }
            }
        }


class LogsModel(BaseModel):
    # its a response model
    cluster: str
    kubernetes: Dict
    log: str
    time: str
    timestamp: str
    unixtime: str


class TrainPortParam(BaseModel):
    port: int = Field(..., description="开放端口号", gt=0, lt=65536)
    protocol: str = Field("http", description="地址协议")


class ExportRecordStatusEnum(str, Enum):
    Running = "Running"
    Succeeded = "Succeeded"
    Failed = "Failed"
    Deleted = "Deleted"
    FileNotFound = 'FileNotFound'


class ExportRecord(TimestampModel, ActiveRecordMixin, table=True):
    __tablename__ = "train_export_record"

    id: int = Field(primary_key=True, index=True)
    task_id: str = Field(..., nullable=False, description="任务ID")
    user_id: str = Field(..., nullable=False, description="用户ID")
    root_user_id: str = Field(..., nullable=False, description="根用户ID")
    download_url: str = Field(..., nullable=False, description="下载地址")
    status: ExportRecordStatusEnum = Field(..., nullable=False, description="状态")
    user_name: Optional[str] = Field(None, nullable=True, description="用户名")
